import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../widgets/pin_screen.dart';
import 'archive_screen.dart';

import '../utils/navigation_utils.dart';

class ArchiveLockScreen extends StatefulWidget {
  const ArchiveLockScreen({Key? key}) : super(key: key);

  @override
  State<ArchiveLockScreen> createState() => _ArchiveLockScreenState();
}

class _ArchiveLockScreenState extends State<ArchiveLockScreen> {
  bool _isBiometricAuthInProgress = false;

  @override
  void initState() {
    super.initState();
    _checkAuthRequirement();
  }

  Future<void> _checkAuthRequirement() async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    // If biometrics are enabled, try authenticating with biometrics first
    if (appLockProvider.isBiometricEnabled && !_isBiometricAuthInProgress) {
      setState(() {
        _isBiometricAuthInProgress = true;
      });
      
      final result = await appLockProvider.authenticateArchiveWithBiometrics();
      
      setState(() {
        _isBiometricAuthInProgress = false;
      });

      // If authentication was successful, navigate directly to archive
      if (result && mounted) {
        Navigator.of(context).pushReplacement(
          NavigationUtils.createSecureRoute(
            context: context,
            screen: const ArchiveScreen(),
          ),
        );
      }
    }
  }

  void _handlePinEntered(String pin) async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    final result = await appLockProvider.authenticateArchiveWithPin(pin);
    
    if (result && mounted) {
      // Instead of returning to home screen, navigate directly to archive
      Navigator.of(context).pushReplacement(
        NavigationUtils.createSecureRoute(
          context: context,
          screen: const ArchiveScreen(),
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Incorrect PIN. Please try again.'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final appLockProvider = Provider.of<AppLockProvider>(context);
    
    // Detect orientation for proper UI layout
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    return WillPopScope(
      // Prevent back button from bypassing the lock
      onWillPop: () async => false,
      child: Scaffold(
        // No app bar for a cleaner look
        body: Stack(
          children: [
            PinScreen(
              title: 'Unlock Archive',
              subtitle: appLockProvider.useAppPinForArchive
                ? 'Enter your app PIN to access archived notes'
                : 'Enter your archive PIN to access archived notes',
              onPinSubmitted: _handlePinEntered,
              showBiometrics: appLockProvider.isBiometricEnabled && !_isBiometricAuthInProgress,
              hideVisibilityToggle: true, // Hide the built-in visibility toggle
              isLandscape: isLandscape, // Pass the orientation information to PinScreen
              onBiometricPressed: appLockProvider.isBiometricEnabled
                ? () async {
                    setState(() {
                      _isBiometricAuthInProgress = true;
                    });
                    
                    final result = await appLockProvider.authenticateArchiveWithBiometrics();
                    
                    setState(() {
                      _isBiometricAuthInProgress = false;
                    });

                    if (result && mounted) {
                      Navigator.of(context).pushReplacement(
                        NavigationUtils.createSecureRoute(
                          context: context,
                          screen: const ArchiveScreen(),
                        ),
                      );
                    }
                  }
                : null,
            ),
            // Top buttons row - positioned with proper spacing
            Positioned(
              top: MediaQuery.of(context).padding.top + 10,
              right: 16,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Close button
                  Container(
                    height: 36,
                    width: 36,
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.black45 : Colors.white60,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.close,
                        color: isDarkMode ? Colors.white : Colors.black87,
                        size: 18,
                      ),
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(false),
                      tooltip: 'Cancel',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
