import 'dart:io';
import 'package:flutter/material.dart';
import '../services/image_picker_service.dart';

class ImageUploadDemo extends StatefulWidget {
  const ImageUploadDemo({super.key});

  @override
  State<ImageUploadDemo> createState() => _ImageUploadDemoState();
}

class _ImageUploadDemoState extends State<ImageUploadDemo> {
  File? _selectedImage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Upload Demo'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            if (_selectedImage != null)
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    _selectedImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              )
            else
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text(
                    'No image selected',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () async {
                final result = await ImagePickerService.showImageSourceDialog(context);
                if (result != null) {
                  final String? action = result['action'] as String?;
                  final File? file = result['file'] as File?;

                  if ((action == 'gallery' || action == 'camera') && file != null) {
                    setState(() {
                      _selectedImage = file;
                    });
                  } else {
                    // Optional: Handle other actions or show a message if needed for the demo
                    print('ImageUploadDemo: Action "$action" not handled or no file provided.');
                  }
                }
              },
              child: const Text('Select Image'),
            ),
            if (_selectedImage != null) ...[
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _selectedImage = null;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear Image'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}