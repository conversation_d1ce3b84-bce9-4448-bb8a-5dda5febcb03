import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../widgets/pin_screen.dart';

class AppLockScreen extends StatefulWidget {
  final Widget child;

  const AppLockScreen({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AppLockScreen> createState() => _AppLockScreenState();
}

class _AppLockScreenState extends State<AppLockScreen> with WidgetsBindingObserver {
  bool _isBiometricAuthInProgress = false;
  
  // We don't need to track orientation changes here anymore
  // The AppLifecycleManager will handle that for us
  
  @override
  void initState() {
    super.initState();
    // We still observe lifecycle events for biometric checks
    WidgetsBinding.instance.addObserver(this);
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Only respond to resume events
    if (state == AppLifecycleState.resumed) {
      // The AppLifecycleManager will handle the decision of whether this is a true resume
      // We just need to check if biometrics should be triggered
      _initialBiometricCheck();
    }
  }

  // This is used for the initial check and app resume - no UI messages
  Future<void> _initialBiometricCheck() async {
    print("DEBUG: AppLockScreen: Starting initial biometric check");
    await Future.delayed(const Duration(milliseconds: 300));
    
    if (!mounted) {
      print("DEBUG: AppLockScreen: Widget not mounted, skipping biometric check");
      return;
    }
    
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    // Initialize the provider if needed
    if (!appLockProvider.isInitialized) {
      print("DEBUG: AppLockScreen: Provider not initialized, initializing");
      await appLockProvider.initialize();
    }
    
    // Check if we need to authenticate and if biometrics are enabled
    final authRequired = await appLockProvider.isAuthenticationRequired();
    print("DEBUG: AppLockScreen: Authentication required: $authRequired");
    print("DEBUG: AppLockScreen: Biometrics enabled: ${appLockProvider.isBiometricEnabled}");
    
    if (!authRequired) {
      print("DEBUG: AppLockScreen: No authentication needed");
      return;
    }
    
    // Check enrollment status if biometrics are enabled
    if (appLockProvider.isBiometricEnabled) {
      final needsEnrollment = await appLockProvider.isBiometricEnrollmentNeeded();
      print("DEBUG: AppLockScreen: Biometric enrollment needed: $needsEnrollment");
    }
    
    // Force a rebuild to show PIN screen instead of automatic biometric auth
    if (mounted) {
      setState(() {
        // Just trigger a rebuild to show PIN entry screen
      });
    }
  }
  
  // This is called from the explicit fingerprint button - shows UI feedback
  Future<void> _authenticateWithBiometrics() async {
    print("DEBUG: AppLockScreen: Manual biometric authentication requested");
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    setState(() {
      _isBiometricAuthInProgress = true;
    });
    
    try {
      print("DEBUG: AppLockScreen: Attempting biometric authentication");
      
      // Try authentication
      final success = await appLockProvider.authenticateWithBiometrics();
      print("DEBUG: AppLockScreen: Biometric authentication result: $success");
      
      if (success) {
        // CRITICAL FIX: Double-check authentication state
        print("DEBUG: AppLockScreen: Authentication reported success");
        
        // Verify that the state was actually updated
        print("DEBUG: AppLockScreen: Checking isAuthenticated = ${appLockProvider.isAuthenticated}");
        
        // If authentication state wasn't updated for some reason, force it
        if (!appLockProvider.isAuthenticated) {
          print("DEBUG: AppLockScreen: State inconsistency detected! Forcing authentication");
          appLockProvider.forceAuthentication();
        }
        
        // Force rebuild
        if (mounted) {
          print("DEBUG: AppLockScreen: Triggering UI rebuild after authentication");
          setState(() {});
        }
      } else {
        // The user canceled or authentication failed - quietly fall back to PIN
        print("DEBUG: AppLockScreen: Authentication canceled or failed - falling back to PIN");
        if (mounted) setState(() {}); // Still update UI to clear any pending states
      }
    } catch (e) {
      print("DEBUG: AppLockScreen: Error in biometric authentication: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Biometric authentication not available'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isBiometricAuthInProgress = false;
        });
        
        // Final check of authentication state
        final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
        print("DEBUG: AppLockScreen: Final auth check, isAuthenticated = ${appLockProvider.isAuthenticated}");
        
        // One last rebuild to ensure UI reflects state
        setState(() {});
      }
    }
  }

  void _handlePinEntered(String pin) async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    final result = await appLockProvider.authenticateWithPin(pin);
    
    if (!result && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Incorrect PIN. Please try again.'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(bottom: 16.0, left: 16.0, right: 16.0),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("DEBUG: AppLockScreen: Building AppLockScreen");
    
    // Get the current orientation
    final orientation = MediaQuery.of(context).orientation;
    print("DEBUG: AppLockScreen: Current orientation: $orientation");
    
    // Force a rebuild every 500ms when authentication is in progress
    if (_isBiometricAuthInProgress) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && _isBiometricAuthInProgress) {
          print("DEBUG: AppLockScreen: Forcing rebuild during authentication");
          setState(() {});
        }
      });
    }
    
    return Consumer<AppLockProvider>(
      builder: (context, appLockProvider, _) {
        // Detailed logs to track authentication state changes
        print("DEBUG: AppLockScreen: *** Consumer rebuilding ***");
        print("DEBUG: AppLockScreen: Lock enabled: ${appLockProvider.isLockEnabled}");
        print("DEBUG: AppLockScreen: Is authenticated: ${appLockProvider.isAuthenticated}");
        print("DEBUG: AppLockScreen: Biometrics enabled: ${appLockProvider.isBiometricEnabled}");

        // Check if lock is enabled and if the user is authenticated
        if (!appLockProvider.isLockEnabled) {
          print("DEBUG: AppLockScreen: Showing main app (lock disabled)");
          return widget.child;
        }

        // Keep PIN UI visible during biometric authentication, like the Archive lock screen
        // We remove the separate loading screen to maintain consistency
        if (appLockProvider.isCheckingAuthState) {
          print("DEBUG: AppLockScreen: Checking auth state, will show PIN UI");
          // Just continue to PIN UI below
        }

        if (appLockProvider.isAuthenticated) {
          print("DEBUG: AppLockScreen: ✓ Authentication successful! Showing main app");
          return widget.child;
        }

        print("DEBUG: AppLockScreen: Not authenticated, showing PIN screen");

        // Show the PIN screen if not authenticated
        return FutureBuilder<bool>(
          future: appLockProvider.isBiometricEnrollmentNeeded(),
          initialData: false, // Assume enrollment not needed by default
          builder: (context, enrollmentSnapshot) {
            final needsEnrollment = enrollmentSnapshot.data ?? false;
            print("DEBUG: AppLockScreen: Biometric enrollment needed: $needsEnrollment");

            // We no longer show the biometric enrollment dialog on the PIN lock screen
            // This allows users to enter their PIN without being interrupted
            // They will see biometric setup guidance only in the app lock settings

            // Create a PIN screen that adapts to the current orientation
            return OrientationBuilder(
              builder: (context, orientation) {
                return PinScreen(
                  title: 'Unlock Deep Note',
                  subtitle: 'Enter your PIN to access your notes',
                  pinLength: 4,
                  onPinSubmitted: _handlePinEntered,
                  showBiometrics: appLockProvider.isBiometricEnabled &&
                      !_isBiometricAuthInProgress &&
                      !needsEnrollment,
                  onBiometricPressed: (appLockProvider.isBiometricEnabled && !needsEnrollment)
                      ? _authenticateWithBiometrics // Use our explicit feedback method
                      : null,
                  // Add orientation-specific styling if needed
                  isLandscape: orientation == Orientation.landscape,
                );
              },
            );
          },
        );
      },
    );
  }
}
