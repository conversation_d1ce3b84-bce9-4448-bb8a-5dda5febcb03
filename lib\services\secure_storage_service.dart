import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/crypto_utils.dart';

/// Service for securely storing and retrieving sensitive app data
class SecureStorageService {
  // App lock keys
  static const String _pinHashKey = 'app_lock_pin_hash';
  static const String _pinSaltKey = 'app_lock_pin_salt';
  static const String _isBiometricEnabledKey = 'app_lock_biometric_enabled';
  static const String _isLockEnabledKey = 'app_lock_enabled';
  static const String _lockTimeoutKey = 'app_lock_timeout';
  static const String _lastActiveTimestampKey = 'app_last_active_timestamp';
  static const String _isBiometricEnrollmentNeededKey = 'biometric_enrollment_needed';
  
  // Archive lock keys
  static const String _isArchiveLockEnabledKey = 'archive_lock_enabled';
  static const String _archivePinHashKey = 'archive_pin_hash';
  static const String _archivePinSaltKey = 'archive_pin_salt';
  static const String _useAppPinForArchiveKey = 'use_app_pin_for_archive';
  static const String _archiveLastActiveTimestampKey = 'archive_last_active_timestamp';

  final FlutterSecureStorage _secureStorage;

  SecureStorageService()
      : _secureStorage = const FlutterSecureStorage(
          aOptions: AndroidOptions(
            encryptedSharedPreferences: true,
          ),
        );

  /// Stores the PIN/password hash securely
  Future<void> setPin(String pin) async {
    final salt = CryptoUtils.generateSalt();
    final hash = CryptoUtils.hashPassword(pin, salt);
    
    await _secureStorage.write(key: _pinHashKey, value: hash);
    await _secureStorage.write(key: _pinSaltKey, value: base64.encode(salt));
  }

  /// Verifies the provided PIN against the stored hash
  Future<bool> verifyPin(String pin) async {
    final hash = await _secureStorage.read(key: _pinHashKey);
    final saltString = await _secureStorage.read(key: _pinSaltKey);
    
    if (hash == null || saltString == null) {
      return false;
    }
    
    final salt = base64.decode(saltString);
    return CryptoUtils.verifyPassword(pin, hash, salt);
  }

  /// Checks if a PIN is set
  Future<bool> isPinSet() async {
    final hash = await _secureStorage.read(key: _pinHashKey);
    return hash != null;
  }

  /// Deletes the stored PIN/password
  Future<void> deletePin() async {
    await _secureStorage.delete(key: _pinHashKey);
    await _secureStorage.delete(key: _pinSaltKey);
  }

  /// Sets whether biometric authentication is enabled
  Future<void> setBiometricEnabled(bool enabled) async {
    await _secureStorage.write(
        key: _isBiometricEnabledKey, value: enabled.toString());
  }

  /// Checks if biometric authentication is enabled
  Future<bool> isBiometricEnabled() async {
    final value = await _secureStorage.read(key: _isBiometricEnabledKey);
    return value == 'true';
  }
  
  /// Sets whether biometrics need to be enrolled at the OS level
  Future<void> setBiometricEnrollmentNeeded(bool needed) async {
    await _secureStorage.write(
        key: _isBiometricEnrollmentNeededKey, value: needed.toString());
  }
  
  /// Checks if biometrics need to be enrolled at the OS level
  Future<bool> isBiometricEnrollmentNeeded() async {
    final value = await _secureStorage.read(key: _isBiometricEnrollmentNeededKey);
    return value == 'true';
  }

  /// Sets whether app lock is enabled
  Future<void> setLockEnabled(bool enabled) async {
    await _secureStorage.write(
        key: _isLockEnabledKey, value: enabled.toString());
  }

  /// Checks if app lock is enabled
  Future<bool> isLockEnabled() async {
    final value = await _secureStorage.read(key: _isLockEnabledKey);
    return value == 'true';
  }

  /// Sets the app lock timeout preference (immediately, 1 minute, 5 minutes, etc.)
  Future<void> setLockTimeout(String timeout) async {
    await _secureStorage.write(key: _lockTimeoutKey, value: timeout);
  }

  /// Gets the app lock timeout preference
  Future<String> getLockTimeout() async {
    final value = await _secureStorage.read(key: _lockTimeoutKey);
    return value ?? 'immediately';
  }

  /// Updates the timestamp of when the app was last active
  Future<void> updateLastActiveTimestamp() async {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    await _secureStorage.write(key: _lastActiveTimestampKey, value: timestamp);
  }

  /// Gets the timestamp of when the app was last active
  Future<DateTime?> getLastActiveTimestamp() async {
    try {
      final value = await _secureStorage.read(key: _lastActiveTimestampKey);
      if (value == null || value.isEmpty) return null;
      
      final timestamp = int.tryParse(value);
      if (timestamp == null) return null;
      
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      // If there's any error reading or parsing the timestamp, return null
      return null;
    }
  }

  //========== Archive Lock Methods ==========//

  /// Sets whether archive lock is enabled
  Future<void> setArchiveLocked(bool enabled) async {
    await _secureStorage.write(
        key: _isArchiveLockEnabledKey, value: enabled.toString());
  }

  /// Checks if archive lock is enabled
  Future<bool> isArchiveLocked() async {
    final value = await _secureStorage.read(key: _isArchiveLockEnabledKey);
    return value == 'true';
  }

  /// Sets whether to use the app PIN for archive lock
  Future<void> setUseAppPinForArchive(bool value) async {
    await _secureStorage.write(
        key: _useAppPinForArchiveKey, value: value.toString());
  }

  /// Checks if using app PIN for archive lock
  Future<bool> useAppPinForArchive() async {
    final value = await _secureStorage.read(key: _useAppPinForArchiveKey);
    // Default to true if not set
    return value == null ? true : value == 'true';
  }

  /// Sets a custom PIN for archive lock
  Future<void> setArchivePin(String pin) async {
    final salt = CryptoUtils.generateSalt();
    final hash = CryptoUtils.hashPassword(pin, salt);
    
    await _secureStorage.write(key: _archivePinHashKey, value: hash);
    await _secureStorage.write(key: _archivePinSaltKey, value: base64.encode(salt));
  }

  /// Verifies the provided archive PIN against the stored hash
  Future<bool> verifyArchivePin(String pin) async {
    final hash = await _secureStorage.read(key: _archivePinHashKey);
    final saltString = await _secureStorage.read(key: _archivePinSaltKey);
    
    if (hash == null || saltString == null) {
      return false;
    }
    
    final salt = base64.decode(saltString);
    return CryptoUtils.verifyPassword(pin, hash, salt);
  }

  /// Checks if an archive PIN is set
  Future<bool> isArchivePinSet() async {
    final hash = await _secureStorage.read(key: _archivePinHashKey);
    return hash != null;
  }

  /// Updates the timestamp of when the archive was last accessed
  Future<void> updateArchiveLastActiveTimestamp() async {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    await _secureStorage.write(key: _archiveLastActiveTimestampKey, value: timestamp);
  }

  /// Gets the timestamp of when the archive was last accessed
  Future<DateTime?> getArchiveLastActiveTimestamp() async {
    final value = await _secureStorage.read(key: _archiveLastActiveTimestampKey);
    if (value == null) return null;
    
    final timestamp = int.tryParse(value);
    if (timestamp == null) return null;
    
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }
}
