import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../models/note.dart';
import '../utils/responsive_helper.dart';
import '../providers/notes_provider.dart';
import 'package:provider/provider.dart';
import 'shared_note_card.dart';

class DraggableNotesGrid extends StatefulWidget {
  final List<Note> notes;
  final Function(Note)? onNotePressed;
  final Function(Note)? onNoteLongPressed;
  final bool isInSelectionMode;
  final Set<int>? selectedNoteIds;
  final bool isPinned;
  final bool isArchived;

  const DraggableNotesGrid({
    Key? key,
    required this.notes,
    this.onNotePressed,
    this.onNoteLongPressed,
    this.isInSelectionMode = false,
    this.selectedNoteIds,
    this.isPinned = false,
    this.isArchived = false,
  }) : super(key: key);

  @override
  State<DraggableNotesGrid> createState() => _DraggableNotesGridState();
}

class _DraggableNotesGridState extends State<DraggableNotesGrid> with TickerProviderStateMixin {
  // Track drag target positions
  List<int> dragPositions = [];
  int? draggingItemIndex;
  Offset? dragOffset;
  bool isDragging = false;
  bool positionChanged = false;
  
  // Animation controllers
  late final AnimationController _scaleController;
  late final AnimationController _elevationController;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize dragPositions based on notes
    resetDragPositions();
    
    // Initialize animation controllers
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    _elevationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOut)
    );
    
    _elevationAnimation = Tween<double>(begin: 2.0, end: 8.0).animate(
      CurvedAnimation(parent: _elevationController, curve: Curves.easeOut)
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _elevationController.dispose();
    super.dispose();
  }
  
  @override
  void didUpdateWidget(DraggableNotesGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.notes.length != widget.notes.length ||
        !_areNotesEqual(oldWidget.notes, widget.notes)) {
      // Always reset drag positions when notes change
      resetDragPositions();
      // Reset drag state if notes changed during drag
      if (isDragging) {
        isDragging = false;
        draggingItemIndex = null;
        dragOffset = null;
        positionChanged = false;
        _scaleController.reset();
        _elevationController.reset();
        // Schedule a rebuild for the next frame instead of calling setState immediately
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {});
          }
        });
      }
    }
  }
  
  bool _areNotesEqual(List<Note> oldNotes, List<Note> newNotes) {
    if (oldNotes.length != newNotes.length) return false;
    
    for (int i = 0; i < oldNotes.length; i++) {
      if (oldNotes[i].id != newNotes[i].id) return false;
    }
    
    return true;
  }
  
  void resetDragPositions() {
    dragPositions = List.generate(widget.notes.length, (index) => index);
  }

  @override
  Widget build(BuildContext context) {
    final notesProvider = Provider.of<NotesProvider>(context);
    final int crossAxisCount = ResponsiveHelper.getGridCrossAxisCount(context);
    final ScrollPhysics parentPhysics = ScrollConfiguration.of(context).getScrollPhysics(context);
    
    // Ensure dragPositions is always valid before building
    if (dragPositions.length != widget.notes.length) {
      resetDragPositions();
    }
    
    // Reset drag state when selection mode changes
    if (widget.isInSelectionMode) {
      isDragging = false;
      positionChanged = false;
    }
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: widget.isInSelectionMode
          ? _buildNonDraggableGrid(crossAxisCount, parentPhysics)
          : _buildDraggableGrid(crossAxisCount, parentPhysics, notesProvider),
    );
  }
  
  Widget _buildNonDraggableGrid(int crossAxisCount, ScrollPhysics parentPhysics) {
    // Regular grid when in selection mode
    return AlignedGridView.count(
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      itemCount: widget.notes.length,
      shrinkWrap: true,
      physics: parentPhysics,
      padding: const EdgeInsets.only(bottom: 80),
      itemBuilder: (context, index) {
        final note = widget.notes[index];
        return SharedNoteCard(
          note: note,
          isSelected: widget.selectedNoteIds?.contains(note.id) ?? false,
          isInSelectionMode: widget.isInSelectionMode,
          onLongPress: widget.onNoteLongPressed != null 
              ? () => widget.onNoteLongPressed!(note) 
              : null,
          onTap: widget.onNotePressed != null
              ? () => widget.onNotePressed!(note)
              : null,
        );
      },
    );
  }
  
  Widget _buildDraggableGrid(int crossAxisCount, ScrollPhysics parentPhysics, NotesProvider notesProvider) {
    return AlignedGridView.count(
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      itemCount: widget.notes.length,
      shrinkWrap: true,
      physics: parentPhysics,
      padding: const EdgeInsets.only(bottom: 80),
      itemBuilder: (context, index) {
        final originalIndex = dragPositions[index];
        final note = widget.notes[originalIndex];
        
        // Use LongPressDraggable to match Google Keep's behavior
        return LongPressDraggable<int>(
          data: originalIndex,
          delay: const Duration(milliseconds: 150),
          // Keep track of the current drag state to determine if it was a drag or just a long press
          onDragStarted: () {
            setState(() {
              draggingItemIndex = originalIndex;
              isDragging = true;
              positionChanged = false;
            });
            _scaleController.forward();
            _elevationController.forward();
          },
          onDragEnd: (details) {
            setState(() {
              // If it was a drag but position didn't change, treat it as a long press selection
              if (!positionChanged && widget.onNoteLongPressed != null && !widget.isInSelectionMode) {
                widget.onNoteLongPressed!(note);
              }
              draggingItemIndex = null;
              dragOffset = null;
              isDragging = false;
            });
            _scaleController.reverse();
            _elevationController.reverse();
          },
          // These handlers track if actual movement happened
          feedback: AnimatedBuilder(
            animation: Listenable.merge([_scaleAnimation, _elevationAnimation]),
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Material(
                  elevation: _elevationAnimation.value,
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width / crossAxisCount - 20,
                    child: SharedNoteCard(
                      note: note,
                      isSelected: false,
                      isInSelectionMode: false,
                      onLongPress: null,
                      onTap: null,
                    ),
                  ),
                ),
              );
            },
          ),
          childWhenDragging: Opacity(
            opacity: 0.2,
            child: SharedNoteCard(
              note: note,
              isSelected: false,
              isInSelectionMode: false,
              onLongPress: null,
              onTap: null,
            ),
          ),
          // Main handlers already defined above
          onDragUpdate: (details) {
            setState(() {
              dragOffset = details.globalPosition;
            });
          },
          // Main handler defined above
          onDragCompleted: () {
            setState(() {
              draggingItemIndex = null;
              dragOffset = null;
            });
            _scaleController.reverse();
            _elevationController.reverse();
            
            // Update note positions in database only if dragPositions is valid
            if (dragPositions.length == widget.notes.length && 
                dragPositions.every((index) => index >= 0 && index < widget.notes.length)) {
              notesProvider.reorderNotesList(
                widget.notes, 
                dragPositions,
                isPinned: widget.isPinned,
                isArchived: widget.isArchived
              );
            } else {
              // Reset dragPositions if invalid
              resetDragPositions();
            }
          },
          child: DragTarget<int>(
            builder: (context, candidateData, rejectedData) {
              return SharedNoteCard(
                note: note,
                isSelected: false,
                isInSelectionMode: false,
                      // Wire up tap handler but not long press (that's handled by LongPressDraggable)
                onLongPress: null,
                onTap: widget.onNotePressed != null
                    ? () => widget.onNotePressed!(note)
                    : null,
              );
            },
            onWillAccept: (data) {
              return data != null && data != originalIndex;
            },
            onAccept: (draggedIndex) {
              setState(() {
                // Safety check: ensure indices are valid
                if (draggedIndex < 0 || draggedIndex >= widget.notes.length ||
                    originalIndex < 0 || originalIndex >= widget.notes.length ||
                    dragPositions.length != widget.notes.length) {
                  resetDragPositions();
                  return;
                }
                
                final targetIndex = dragPositions.indexOf(originalIndex);
                final sourceIndex = dragPositions.indexOf(draggedIndex);
                
                // Additional safety check for indexOf results
                if (targetIndex == -1 || sourceIndex == -1 ||
                    targetIndex >= dragPositions.length || sourceIndex >= dragPositions.length) {
                  resetDragPositions();
                  return;
                }
                
                // Reorder the list
                final element = dragPositions.removeAt(sourceIndex);
                dragPositions.insert(targetIndex, element);
                
                // Mark that position actually changed
                positionChanged = true;
              });
            },
          ),
        );
      },
    );
  }
}
