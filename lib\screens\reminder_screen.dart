import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notes_provider.dart';
import '../providers/theme_provider.dart';
import '../models/note.dart';
import '../utils/constants.dart';
import '../utils/date_formatter.dart';
import '../utils/navigation_utils.dart';
import '../widgets/note_item.dart';
import '../widgets/draggable_notes_grid.dart';
import '../widgets/draggable_notes_list.dart';
import 'note_detail_screen.dart';
import 'package:hugeicons/hugeicons.dart';

class ReminderScreen extends StatefulWidget {
  const ReminderScreen({super.key});

  @override
  State<ReminderScreen> createState() => _ReminderScreenState();
}

class _ReminderScreenState extends State<ReminderScreen> {
  bool _isMultiSelectMode = false;
  Set<int> _selectedNoteIds = {};
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    // Fetch notes when the screen is initialized
    Future.microtask(
        () => Provider.of<NotesProvider>(context, listen: false).fetchNotes());
  }

  void _toggleNoteSelection(int noteId) {
    setState(() {
      if (_selectedNoteIds.contains(noteId)) {
        _selectedNoteIds.remove(noteId);
        if (_selectedNoteIds.isEmpty) {
          _isMultiSelectMode = false;
        }
      } else {
        _selectedNoteIds.add(noteId);
      }
    });
  }

  void _enterMultiSelectMode(int noteId) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedNoteIds.add(noteId);
    });
  }

  void _exitMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedNoteIds.clear();
    });
  }

  void _navigateToNoteDetail(int noteId) {
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: NoteDetailScreen(noteId: noteId),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<NotesProvider, ThemeProvider>(
      builder: (context, notesProvider, themeProvider, child) {
        final isDarkMode = themeProvider.isDarkMode;
        final textColor = isDarkMode ? Colors.white : Colors.black87;
        final backgroundColor = isDarkMode ? const Color(0xFF121212) : const Color(0xFFFAFAFA);
        
        // Filter notes that have reminders and are not archived or deleted
        final reminderNotes = notesProvider.notes
            .where((note) => note.hasReminder && !note.isArchived && !note.isDeleted)
            .toList();
        
        // Sort by reminder time (earliest first)
        reminderNotes.sort((a, b) {
          if (a.reminderTime == null && b.reminderTime == null) return 0;
          if (a.reminderTime == null) return 1;
          if (b.reminderTime == null) return -1;
          return a.reminderTime!.compareTo(b.reminderTime!);
        });

        return Scaffold(
          backgroundColor: backgroundColor,
          appBar: AppBar(
            backgroundColor: backgroundColor,
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back_ios_rounded,
                color: textColor,
                size: 20,
              ),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              'Reminders',
              style: TextStyle(
                color: textColor,
                fontSize: 24,
                fontWeight: FontWeight.w700,
              ),
            ),
            actions: [
              if (!_isMultiSelectMode)
                IconButton(
                  icon: Icon(
                    _isGridView ? Icons.view_list : Icons.grid_view,
                    color: textColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _isGridView = !_isGridView;
                    });
                  },
                ),
              if (_isMultiSelectMode)
                IconButton(
                  icon: Icon(Icons.close, color: textColor),
                  onPressed: _exitMultiSelectMode,
                ),
            ],
          ),
          body: reminderNotes.isEmpty
              ? _buildEmptyState(textColor)
              : _isGridView
                  ? _buildGridView(reminderNotes)
                  : _buildListView(reminderNotes),
        );
      },
    );
  }

  Widget _buildEmptyState(Color textColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.accent.withOpacity(0.1), AppColors.accent.withOpacity(0.05)],
              ),
              shape: BoxShape.circle,
            ),
            child: HugeIcon(
              icon: HugeIcons.strokeRoundedAlarmClock,
              color: AppColors.accent,
              size: 48,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Reminders Yet',
            style: TextStyle(
              color: textColor,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create a note with a reminder to see it here',
            style: TextStyle(
              color: textColor.withOpacity(0.6),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGridView(List<Note> notes) {
    return DraggableNotesGrid(
      notes: notes,
      isPinned: false,
      isArchived: false,
      isInSelectionMode: _isMultiSelectMode,
      selectedNoteIds: _selectedNoteIds,
      onNotePressed: (note) {
        if (_isMultiSelectMode) {
          if (note.id != null) {
            _toggleNoteSelection(note.id!);
          }
        } else {
          _navigateToNoteDetail(note.id!);
        }
      },
      onNoteLongPressed: (note) {
        if (note.id != null) {
          _enterMultiSelectMode(note.id!);
        }
      },
    );
  }

  Widget _buildListView(List<Note> notes) {
    return DraggableNotesList(
      notes: notes,
      isPinned: false,
      isArchived: false,
      isInSelectionMode: _isMultiSelectMode,
      selectedNoteIds: _selectedNoteIds,
      onNotePressed: (note) {
        if (_isMultiSelectMode) {
          if (note.id != null) {
            _toggleNoteSelection(note.id!);
          }
        } else {
          _navigateToNoteDetail(note.id!);
        }
      },
      onNoteLongPressed: (note) {
        if (note.id != null) {
          _enterMultiSelectMode(note.id!);
        }
      },
    );
  }

}