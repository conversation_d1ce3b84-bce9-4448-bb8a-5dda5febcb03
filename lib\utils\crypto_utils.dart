import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

class CryptoUtils {
  static const int _saltLength = 32;
  static const int _keyLength = 32;
  static const int _iterations = 10000;

  /// Generate a secure random salt
  static Uint8List generateSalt() {
    final random = Random.secure();
    final salt = Uint8List(_saltLength);
    for (int i = 0; i < _saltLength; i++) {
      salt[i] = random.nextInt(256);
    }
    return salt;
  }

  /// Hash a password with salt using PBKDF2
  static String hashPassword(String password, Uint8List salt) {
    final hash = _pbkdf2(password, salt, _iterations, _keyLength);
    return base64.encode(hash);
  }

  /// Verify a password against a hash
  static bool verifyPassword(String password, String hash, Uint8List salt) {
    final computedHash = hashPassword(password, salt);
    return computedHash == hash;
  }

  /// Derive an encryption key from password and salt
  static Uint8List deriveKey(String password, Uint8List salt) {
    return _pbkdf2(password, salt, _iterations, _keyLength);
  }

  /// Generate a secure random IV for AES encryption
  static Uint8List generateIV() {
    final random = Random.secure();
    final iv = Uint8List(16); // AES block size
    for (int i = 0; i < 16; i++) {
      iv[i] = random.nextInt(256);
    }
    return iv;
  }

  /// PBKDF2 implementation using HMAC-SHA256
  static Uint8List _pbkdf2(String password, Uint8List salt, int iterations, int keyLength) {
    final passwordBytes = utf8.encode(password);
    final hLen = 32; // SHA-256 output length
    final l = (keyLength / hLen).ceil();
    final r = keyLength - (l - 1) * hLen;

    final derivedKey = <int>[];

    for (int i = 1; i <= l; i++) {
      final u = _f(passwordBytes, salt, i, iterations);
      derivedKey.addAll(u.take(i == l ? r : hLen));
    }

    return Uint8List.fromList(derivedKey.take(keyLength).toList());
  }

  static List<int> _f(List<int> password, Uint8List salt, int c, int iterations) {
    final saltWithCounter = Uint8List(salt.length + 4);
    saltWithCounter.setRange(0, salt.length, salt);
    saltWithCounter.buffer.asByteData().setUint32(salt.length, c, Endian.big);

    var u = _hmacSha256(password, saltWithCounter);
    final result = List<int>.from(u);

    for (int i = 1; i < iterations; i++) {
      u = _hmacSha256(password, Uint8List.fromList(u));
      for (int j = 0; j < u.length; j++) {
        result[j] ^= u[j];
      }
    }

    return result;
  }

  static List<int> _hmacSha256(List<int> key, Uint8List data) {
    final hmac = Hmac(sha256, key);
    return hmac.convert(data).bytes;
  }
}