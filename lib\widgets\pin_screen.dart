import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../utils/constants.dart';

/// A modern, professionally designed PIN entry screen with responsive layout
class PinScreen extends StatefulWidget {
  /// Title shown at the top of the screen
  final String title;

  /// Subtitle or instruction text
  final String subtitle;

  /// Number of PIN digits (default: 4)
  final int pinLength;

  /// Callback when PIN is entered
  final Function(String) onPinSubmitted;

  /// Function to handle biometric authentication
  final Function()? onBiometricPressed;

  /// Whether biometric authentication is available
  final bool showBiometrics;
  
  /// Whether to hide the visibility toggle button
  final bool hideVisibilityToggle;
  
  /// Whether the screen is in landscape mode
  final bool isLandscape;

  const PinScreen({
    Key? key,
    required this.title,
    required this.subtitle,
    this.pinLength = 4,
    required this.onPinSubmitted,
    this.onBiometricPressed,
    this.showBiometrics = false,
    this.hideVisibilityToggle = false,
    this.isLandscape = false,
  }) : super(key: key);

  @override
  State<PinScreen> createState() => _PinScreenState();
}

class _PinScreenState extends State<PinScreen> with SingleTickerProviderStateMixin {
  String _pin = '';

  // Animation controller for PIN entry feedback
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }
  
  @override
  void didUpdateWidget(PinScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Only use MediaQuery for orientation detection if needed
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate available space
            final availableHeight = constraints.maxHeight;
            final availableWidth = constraints.maxWidth;
            
            // Determine if we're on a very small screen
            final isVerySmallScreen = availableHeight < 500;
            
            // Calculate adaptive scaling factors
            final sizeMultiplier = math.min(1.0, availableHeight / 800);
            final sizeFactor = math.max(0.7, sizeMultiplier); // Never go below 70%
            
            // Use a different layout for landscape mode
            if (widget.isLandscape) {
              return _buildLandscapeLayout(
                availableHeight, 
                availableWidth, 
                sizeFactor, 
                isDarkMode
              );
            }
            
            // Use a more flexible layout approach with a ScrollView for small screens
            return SingleChildScrollView(
              physics: isVerySmallScreen 
                ? const AlwaysScrollableScrollPhysics() 
                : const NeverScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: availableHeight,
                ),
                child: IntrinsicHeight( 
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24 * sizeFactor),
                    child: Column(
                      children: [
                        // Top spacing
                        SizedBox(height: 32 * sizeFactor),
                        
                        // Lock icon in an accent-colored container
                        SizedBox(height: isVerySmallScreen ? 8 * sizeFactor : 24 * sizeFactor),
                        _buildLockIcon(sizeFactor, isDarkMode),
                        
                        // Title and subtitle
                        SizedBox(height: 20 * sizeFactor),
                        Text(
                          widget.title,
                          style: TextStyle(
                            fontSize: 28 * sizeFactor,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8 * sizeFactor),
                        Text(
                          widget.subtitle,
                          style: TextStyle(
                            fontSize: 16 * sizeFactor,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        // Expanded space that adapts based on screen size
                        SizedBox(height: isVerySmallScreen ? 24 * sizeFactor : 40 * sizeFactor),
                        
                        // PIN indicator dots
                        _buildPinIndicator(sizeFactor),
                        
                        // Flexible spacer that pushes keypad to the bottom on tall screens
                        Spacer(flex: isVerySmallScreen ? 1 : 2),
                        
                        // Number keypad
                        _buildKeypad(sizeFactor, isDarkMode),
                        
                        // Bottom section with biometric authentication
                        SizedBox(height: 20 * sizeFactor),
                        _buildBiometricButton(sizeFactor, isDarkMode),
                        SizedBox(height: 20 * sizeFactor),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
  
  // Build a landscape-optimized layout with improved responsiveness
  Widget _buildLandscapeLayout(
    double availableHeight, 
    double availableWidth, 
    double sizeFactor,
    bool isDarkMode
  ) {
    // Adaptive sizing based on screen dimensions
    final isWideScreen = availableWidth > 900;
    final isNarrowScreen = availableWidth < 600;
    
    // Adaptive sizing for landscape orientation
    final landscapeSizeFactor = isNarrowScreen 
        ? math.max(sizeFactor * 0.8, 0.65) 
        : isWideScreen 
            ? math.min(sizeFactor * 1.2, 1.0)
            : math.max(sizeFactor * 0.9, 0.7);
    
    // Optimized flex ratios based on screen width
    final leftFlex = isWideScreen ? 5 : isNarrowScreen ? 4 : 5;
    final rightFlex = isWideScreen ? 4 : isNarrowScreen ? 5 : 4;
    
    // Calculate shadow opacity based on theme
    final shadowOpacity = isDarkMode ? 0.3 : 0.1;
    
    return Container(
      width: availableWidth,
      height: availableHeight,
      color: isDarkMode ? Color(0xFF121212) : Colors.grey[50],
      child: Row(
        children: [
          // Left side: Header and PIN indicators
          Expanded(
            flex: leftFlex,
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? Color(0xFF1A1A1A) : Colors.white,
                boxShadow: [
                  if (!isDarkMode)
                    BoxShadow(
                      color: Colors.black.withOpacity(shadowOpacity),
                      blurRadius: 8,
                      offset: Offset(2, 0),
                    ),
                ],
              ),
              margin: EdgeInsets.only(
                right: isWideScreen ? 24 : 16, 
                top: 16, 
                bottom: 16, 
                left: 16
              ),
              padding: EdgeInsets.all(isWideScreen ? 32 : 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Lock icon with adjusted size
                  _buildLockIcon(
                    isWideScreen 
                        ? landscapeSizeFactor * 1.4 
                        : isNarrowScreen 
                            ? landscapeSizeFactor * 1.1 
                            : landscapeSizeFactor * 1.3, 
                    isDarkMode
                  ),
                  
                  // Title with responsive sizing
                  SizedBox(height: isNarrowScreen ? 16 * landscapeSizeFactor : 24 * landscapeSizeFactor),
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: isWideScreen 
                          ? 32 * landscapeSizeFactor 
                          : isNarrowScreen 
                              ? 24 * landscapeSizeFactor 
                              : 28 * landscapeSizeFactor,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  // Subtitle with responsive spacing
                  SizedBox(height: isNarrowScreen ? 12 * landscapeSizeFactor : 16 * landscapeSizeFactor),
                  Text(
                    widget.subtitle,
                    style: TextStyle(
                      fontSize: isNarrowScreen 
                          ? 14 * landscapeSizeFactor 
                          : 16 * landscapeSizeFactor,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  // PIN indicator with improved spacing
                  SizedBox(height: isNarrowScreen ? 24 * landscapeSizeFactor : 32 * landscapeSizeFactor),
                  _buildPinIndicator(landscapeSizeFactor * 1.2),
                  
                  // Biometric button with adaptive sizing
                  if (widget.showBiometrics)
                    SizedBox(height: isNarrowScreen ? 24 * landscapeSizeFactor : 32 * landscapeSizeFactor),
                  if (widget.showBiometrics)
                    _buildBiometricButton(
                      isWideScreen 
                          ? landscapeSizeFactor * 1.2 
                          : landscapeSizeFactor * 1.0,
                      isDarkMode
                    ),
                ],
              ),
            ),
          ),
          
          // Right side: Keypad with card-like elevation in light mode
          Expanded(
            flex: rightFlex,
            child: Container(
              margin: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode ? Color(0xFF1A1A1A) : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  if (!isDarkMode)
                    BoxShadow(
                      color: Colors.black.withOpacity(shadowOpacity),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                ],
              ),
              padding: EdgeInsets.symmetric(
                horizontal: isWideScreen ? 24 : 16,
                vertical: isWideScreen ? 24 : 16,
              ),
              child: Center(
                child: _buildKeypad(
                  isWideScreen 
                      ? landscapeSizeFactor * 1.2 
                      : isNarrowScreen 
                          ? landscapeSizeFactor * 0.9 
                          : landscapeSizeFactor * 1.1,
                  isDarkMode,
                  isLandscape: true
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Custom lock icon with glow effect
  Widget _buildLockIcon(double sizeFactor, bool isDarkMode) {
    return Container(
      width: 100 * sizeFactor,
      height: 100 * sizeFactor,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.accent.withOpacity(0.2),
        boxShadow: [
          BoxShadow(
            color: AppColors.accent.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Center(
        child: Icon(
          Icons.lock_outline_rounded,
          size: 50 * sizeFactor,
          color: AppColors.accent,
        ),
      ),
    );
  }
  
  // Modern, compact biometric button
  Widget _buildBiometricButton(double sizeFactor, bool isDarkMode) {
    if (!widget.showBiometrics || widget.onBiometricPressed == null) {
      return const SizedBox.shrink();
    }
    
    return GestureDetector(
      onTap: widget.onBiometricPressed,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: 12 * sizeFactor,
          horizontal: 16 * sizeFactor,
        ),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.transparent : Colors.white,
          borderRadius: BorderRadius.circular(30 * sizeFactor),
          border: Border.all(
            color: AppColors.accent.withOpacity(0.5),
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fingerprint,
              size: 22 * sizeFactor,
              color: AppColors.accent,
            ),
            SizedBox(width: 8 * sizeFactor),
            Text(
              'Use Biometrics',
              style: TextStyle(
                fontSize: 16 * sizeFactor,
                fontWeight: FontWeight.w500,
                color: AppColors.accent,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // PIN indicator dots
  Widget _buildPinIndicator(double sizeFactor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.pinLength,
        (index) {
          final bool isFilled = _pin.length > index;
          final isDarkMode = Theme.of(context).brightness == Brightness.dark;
          return AnimatedContainer(
            duration: const Duration(milliseconds: 150),
            margin: EdgeInsets.symmetric(horizontal: 8 * sizeFactor),
            width: 16 * sizeFactor,
            height: 16 * sizeFactor,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isFilled
                  ? AppColors.accent
                  : Colors.transparent,
              border: Border.all(
                color: isFilled 
                  ? AppColors.accent 
                  : isDarkMode ? Colors.grey.withOpacity(0.5) : Colors.grey.withOpacity(0.7),
                width: 1.5 * sizeFactor,
              ),
            ),
          );
        },
      ),
    );
  }
  
  // Keypad with responsive sizing
  Widget _buildKeypad(double sizeFactor, bool isDarkMode, {bool isLandscape = false}) {
    // Define all keypad buttons with their values
    final keypadButtons = [
      '1', '2', '3',
      '4', '5', '6',
      '7', '8', '9',
      'clear', '0', 'backspace',
    ];
    
    // Calculate optimal button size based on available space
    final buttonBaseSize = isLandscape ? 75.0 : 70.0;
    final buttonSize = buttonBaseSize * sizeFactor;
    final fontSize = buttonSize * 0.45;
    final iconSize = buttonSize * 0.4;
    
    // Use more contrasting colors for light mode to ensure text visibility
    final buttonColor = isDarkMode ? Color(0xFF1E1E1E) : Color(0xFFF5F5F5);
    final buttonBorderColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    
    // Adjust spacing for landscape mode
    final horizontalSpacing = isLandscape ? 12.0 * sizeFactor : 16.0 * sizeFactor;
    final verticalSpacing = isLandscape ? 12.0 * sizeFactor : 16.0 * sizeFactor;
    
    // Use a Column with Rows instead of GridView to avoid layout issues
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Row 1: 1, 2, 3
        _buildKeypadRow(
          [keypadButtons[0], keypadButtons[1], keypadButtons[2]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
          isDarkMode,
          textColor,
        ),
        SizedBox(height: verticalSpacing),
        
        // Row 2: 4, 5, 6
        _buildKeypadRow(
          [keypadButtons[3], keypadButtons[4], keypadButtons[5]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
          isDarkMode,
          textColor,
        ),
        SizedBox(height: verticalSpacing),
        
        // Row 3: 7, 8, 9
        _buildKeypadRow(
          [keypadButtons[6], keypadButtons[7], keypadButtons[8]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
          isDarkMode,
          textColor,
        ),
        SizedBox(height: verticalSpacing),
        
        // Row 4: empty, 0, backspace
        _buildKeypadRow(
          [keypadButtons[9], keypadButtons[10], keypadButtons[11]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
          isDarkMode,
          textColor,
        ),
      ],
    );
  }
  
  // Helper method to build a row of keypad buttons
  Widget _buildKeypadRow(
    List<String> keys,
    double buttonSize,
    double fontSize,
    double iconSize,
    Color buttonColor,
    Color borderColor,
    double sizeFactor,
    double horizontalSpacing,
    bool isDarkMode,
    Color textColor,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: keys.map((key) {
        // Empty button placeholder
        if (key.isEmpty) {
          return SizedBox(width: buttonSize, height: buttonSize);
        }
        
        // Clear button
        if (key == 'clear') {
          return _buildKeypadButton(
            child: Text(
              'Clear',
              style: TextStyle(
                fontSize: fontSize * 0.7,
                fontWeight: FontWeight.w600,
                color: AppColors.accent,
              ),
            ),
            onTap: _clearPin,
            size: buttonSize,
            color: buttonColor,
            borderColor: borderColor,
            horizontalMargin: horizontalSpacing / 2,
          );
        }
        
        // Backspace button
        if (key == 'backspace') {
          return _buildKeypadButton(
            child: Icon(
              Icons.backspace_outlined,
              size: iconSize,
              color: AppColors.accent,
            ),
            onTap: _handleBackspace,
            size: buttonSize,
            color: buttonColor,
            borderColor: borderColor,
            horizontalMargin: horizontalSpacing / 2,
          );
        }
        
        // Number button with improved visibility in light mode
        return _buildKeypadButton(
          child: Text(
            key,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
          onTap: () => _handleDigitPress(key),
          size: buttonSize,
          color: buttonColor,
          borderColor: borderColor,
          horizontalMargin: horizontalSpacing / 2,
        );
      }).toList(),
    );
  }
  
  // Helper method to build consistent keypad buttons
  Widget _buildKeypadButton({
    required Widget child,
    required VoidCallback onTap,
    required double size,
    required Color color,
    required Color borderColor,
    double horizontalMargin = 8.0,
  }) {
    return GestureDetector(
      onTap: () {
        // Add haptic feedback
        HapticFeedback.lightImpact();
        
        // Animate button press
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
        
        // Call the provided onTap callback
        onTap();
      },
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          width: size,
          height: size,
          margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
            border: Border.all(color: borderColor, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(child: child),
        ),
      ),
    );
  }
  
  // Handle digit press
  void _handleDigitPress(String digit) {
    // Only add digit if PIN length is less than the required length
    if (_pin.length < widget.pinLength) {
      setState(() {
        _pin += digit;
      });
      
      // If PIN is complete, submit it
      if (_pin.length == widget.pinLength) {
        widget.onPinSubmitted(_pin);
      }
    }
  }
  
  // Handle backspace press
  void _handleBackspace() {
    // Only remove digit if PIN is not empty
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
      });
    }
  }
  
  // Clear PIN
  void _clearPin() {
    setState(() {
      _pin = '';
    });
  }
}
