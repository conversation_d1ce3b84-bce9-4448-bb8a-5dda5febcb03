import 'package:flutter/material.dart';
import '../utils/navigation_utils.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import '../models/note.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../screens/note_detail_screen.dart';
import '../utils/date_formatter.dart';
import '../utils/color_utils.dart';
import 'package:hugeicons/hugeicons.dart';

class SharedNoteCard extends StatefulWidget {
  final Note note;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool isInSelectionMode;
  final VoidCallback? onTap;
  final bool showPinIcon;
  final VoidCallback? onPinTap;
  final List<Widget> actionButtons;

  const SharedNoteCard({
    Key? key,
    required this.note,
    this.onLongPress,
    this.isSelected = false,
    this.isInSelectionMode = false,
    this.onTap,
    this.showPinIcon = true,
    this.onPinTap,
    this.actionButtons = const [],
  }) : super(key: key);

  @override
  State<SharedNoteCard> createState() => _SharedNoteCardState();
}

class _SharedNoteCardState extends State<SharedNoteCard> with SingleTickerProviderStateMixin {
  List<Label> _noteLabels = [];
  bool _isLoadingLabels = true;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  String _contentPreview = '';
  List<String> _imageUrls = [];

  @override
  void initState() {
    super.initState();
    _loadLabelsForNote();
    _parseContentPreview();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    // Create scale animation for card press effect
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(

      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void didUpdateWidget(SharedNoteCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if note content has changed or if the note ID has changed
    if (oldWidget.note.id != widget.note.id || 
        oldWidget.note.content != widget.note.content ||
        oldWidget.note.updatedAt != widget.note.updatedAt) {
      // Re-parse content to extract images and text
      _parseContentPreview();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _parseContentPreview() {
    try {
      if (widget.note.content.isEmpty) {
        _contentPreview = '';
        _imageUrls = [];
        return;
      }

      // Try to parse the Delta JSON
      final dynamic delta = jsonDecode(widget.note.content);
      
      if (delta is List && delta.isNotEmpty) {
        // Process Delta operations
        StringBuffer plainText = StringBuffer();
        int charCount = 0;
        bool textPreviewComplete = false;
        _imageUrls = [];
        
        // First pass: collect images regardless of position (max 8 images for performance)
        // Google Keep style - show images even if they're after text
        final int maxImagesToCollect = 8;
        for (var op in delta) {
          if (op is Map && op.containsKey('insert')) {
            final insert = op['insert'];
            
            // If this is an image, add it to our collection
            if (insert is Map && insert.containsKey('image')) {
              final imageUrl = insert['image'];
              if (imageUrl is String && imageUrl.isNotEmpty) {
                _imageUrls.add(imageUrl);
                
                // Limit the number of images we collect for performance
                if (_imageUrls.length >= maxImagesToCollect) {
                  break;
                }
              }
            }
          }
        }
        
        // Second pass: collect text for preview (limited)
        for (var op in delta) {
          // Stop if we've already collected enough text
          if (textPreviewComplete) {
            break;
          }
          
          if (op is Map && op.containsKey('insert')) {
            final insert = op['insert'];
            
            if (insert is String) {
              plainText.write(insert);
              charCount += insert.length;
              
              // Stop collecting text once we have enough for preview
              if (charCount > 150) {
                textPreviewComplete = true;
              }
            }
          }
        }
        
        String text = plainText.toString();
        
        // Smart truncation - try to break at sentence or paragraph
        if (text.length > 100) {
          // First try to find a paragraph break
          int paraBreak = text.indexOf('\n', 80);
          if (paraBreak > 0 && paraBreak < 150) {
            text = text.substring(0, paraBreak);
          } else {
            // Try to find a sentence break
            int sentenceBreak = -1;
            for (var endChar in ['. ', '! ', '? ']) {
              int idx = text.indexOf(endChar, 80);
              if (idx > 0 && idx < 150 && (sentenceBreak == -1 || idx < sentenceBreak)) {
                sentenceBreak = idx + 1; // Include the punctuation
              }
            }
            
            if (sentenceBreak > 0) {
              text = text.substring(0, sentenceBreak);
            } else {
              // If no sentence break, just truncate with ellipsis
              text = text.substring(0, 100) + '...';
            }
          }
        }

        // Clean up whitespace
        text = text.replaceAll(RegExp(r'\n{2,}'), '\n');
        text = text.trim();
        
        _contentPreview = text;
      } else {
        // Fallback for non-delta content
        _contentPreview = widget.note.content.length > 100 
            ? '${widget.note.content.substring(0, 100)}...'
            : widget.note.content;
      }
    } catch (e) {
      // If parsing fails, use a simple approach
      _contentPreview = widget.note.content.length > 100 
          ? '${widget.note.content.substring(0, 100)}...'
          : widget.note.content;
    }
  }

  Future<void> _loadLabelsForNote() async {
    if (widget.note.id != null) {
      setState(() {
        _isLoadingLabels = true;
      });

      try {
        final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
        final labels = await labelsProvider.getLabelsForNote(widget.note.id!);
        
        if (mounted) {
          setState(() {
            _noteLabels = labels;
            _isLoadingLabels = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingLabels = false;
          });
        }
      }
    } else {
      setState(() {
        _isLoadingLabels = false;
      });
    }
  }

  Widget _buildImagePreview() {
    if (_imageUrls.isEmpty) return const SizedBox.shrink();
    
    // Use LayoutBuilder to get available width
    return LayoutBuilder(builder: (context, constraints) {
      // Calculate safe dimensions based on available width
      final availableWidth = constraints.maxWidth;
      
      // Calculate adaptive height based on width to maintain reasonable aspect ratio
      // but with a maximum cap to prevent excessively tall images
      final double safeMaxHeight = _imageUrls.length == 1 ? 
          math.min(availableWidth * 0.7, 150.0) : // For single image, height should be proportional to width
          math.min(availableWidth * 0.5, 120.0);  // For multiple images, more compact
      
      if (_imageUrls.length == 1) {
        // Single image layout - use ClipRRect with top corners only
        return ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          child: SizedBox(
            width: availableWidth, // Use exactly available width
            height: safeMaxHeight, // Use calculated height
            child: _buildImageWidget(_imageUrls.first),
          ),
        );
      } else {
        // Multiple images grid layout - ensure proper clipping
        int crossAxisCount = _imageUrls.length == 2 ? 2 : 3;
        double aspectRatio = _imageUrls.length == 2 ? 1.5 : 1.0;
        
        // Calculate grid item size to ensure it fits within constraints
        final itemWidth = (availableWidth - (crossAxisCount - 1) * 4) / crossAxisCount;
        final itemHeight = itemWidth / aspectRatio;
        
        // Calculate total grid height (add a small margin for the spacing)
        final rowCount = (_imageUrls.length <= 3) ? 1 : 2;
        final gridHeight = math.min(
          (itemHeight * rowCount) + ((rowCount - 1) * 4),
          safeMaxHeight
        );
        
        return ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          child: SizedBox(
            height: gridHeight,
            width: availableWidth,
            child: GridView.count(
              padding: EdgeInsets.zero, // Ensure no internal padding
              crossAxisCount: crossAxisCount,
              mainAxisSpacing: 4,
              crossAxisSpacing: 4,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: _imageUrls.take(math.min(6, _imageUrls.length)).map((url) => _buildImageThumbnail(url)).toList()
                // Add 'more' indicator if needed
                + (_imageUrls.length > 6 ? [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black45,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        '+${_imageUrls.length - 5}',
                        style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ),
                  )
                ] : []),
            ),
          ),
        );
      }
    });
  }
  
  Widget _buildImageThumbnail(String url) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4),
      child: _buildImageWidget(url),
    );
  }
  
  Widget _buildImageWidget(String imageUrl) {
    // Determine if this is a network image or a local image
    final bool isNetworkImage = imageUrl.startsWith('http');
    
    // Wrap with error boundary to catch any rendering exceptions
    return LayoutBuilder(builder: (context, constraints) {
      final Widget imageWidget;
      
      if (isNetworkImage) {
        imageWidget = Image.network(
          imageUrl,
          fit: BoxFit.cover,
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          errorBuilder: (context, error, stackTrace) => Container(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            color: Colors.grey[200],
            child: const Icon(Icons.broken_image, color: Colors.grey),
          ),
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              color: Colors.grey[100],
              child: const Center(child: CircularProgressIndicator()),
            );
          },
        );
      } else {
        imageWidget = Image.file(
          File(imageUrl),
          fit: BoxFit.cover,
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          errorBuilder: (context, error, stackTrace) => Container(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            color: Colors.grey[200],
            child: const Icon(Icons.broken_image, color: Colors.grey),
          ),
        );
      }
      
      // Wrap with a SizedBox to enforce constraints and prevent overflow
      return SizedBox(
        width: constraints.maxWidth,
        height: constraints.maxHeight,
        child: imageWidget,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Determine card background color with improved readability
    Color cardColor;
    Color textColor;
    Color accentColor;
    
    if (widget.note.themeColor != null) {
      // Get the original color from the note
      final originalColor = Color(int.parse(widget.note.themeColor!));
      
      // Create a readable background version of the color
      cardColor = ColorUtils.getBackgroundVersion(originalColor, isDarkTheme: isDarkMode);
      
      // Create an accent color for elements like borders, pins, etc.
      accentColor = ColorUtils.getAccentVersion(originalColor, isDarkTheme: isDarkMode);
      
      // Determine the best text color for readability
      textColor = ColorUtils.getTextColorForBackground(cardColor);
    } else {
      // Default colors when no theme is applied
      cardColor = isDarkMode ? const Color(0xFF2D2D2D) : Colors.white;
      textColor = isDarkMode ? Colors.white : Colors.black87;
      accentColor = isDarkMode ? Colors.white30 : Colors.black12;
    }
    
    // No borders used anymore as we've switched to a flat design
    // We'll handle pinned notes with different styling directly in the Container
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTapDown: (_) => _animationController.forward(),
        onTapUp: (_) => _animationController.reverse(),
        onTapCancel: () => _animationController.reverse(),
        onLongPress: widget.onLongPress,
        onTap: () {
          _animationController.reverse();
          if (widget.onTap != null) {
            widget.onTap!();
          } else if (widget.note.id != null) {
            Navigator.push(
              context,
              NavigationUtils.createSecureRoute(
                context: context,
                screen: NoteDetailScreen(noteId: widget.note.id),
              ),
            );
          }
        },
        child: Stack(
          children: [
            // Pure Google Keep style - absolutely flat with no decorations
            Container(
              // Minimal margin
              margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
              // Clip content to rounded rectangle shape
              clipBehavior: Clip.antiAlias,
              // Add overflow protection
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width - 8, // Full width minus margins
              ),
              decoration: BoxDecoration(
                // Only keep the color and rounded corners, nothing else
                color: cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // Use minimum space needed
                children: [
                  // ALWAYS show images at the top (Google Keep style)
                  // regardless of their position in the note content
                  if (_imageUrls.isNotEmpty) _buildImagePreview(),
                  
                  // Note content
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min, // Use minimum space needed
                          children: [
                            // Title row with pin icon
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Title (expanded to take available space)
                                Expanded(
                                  child: widget.note.title.isNotEmpty
                                      ? Text(
                                          widget.note.title,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: textColor, // Use adaptive text color
                                          ),
                                          maxLines: 1, // Reduce to 1 line
                                          overflow: TextOverflow.ellipsis,
                                        )
                                      : const SizedBox.shrink(),
                                ),
                                
                                // Pin icon (only show if note is pinned and not in selection mode)
                                if (!widget.isInSelectionMode && widget.showPinIcon && widget.note.isPinned)
                                  // Pin icon (indicator only, not interactive)
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4.0),
                                    child: HugeIcon(
                                      icon: HugeIcons.strokeRoundedPin,
                                      size: 20,
                                      color: widget.note.themeColor != null ? accentColor : (isDarkMode ? Colors.amber : Colors.orange.shade700),
                                    ),
                                  ),
                              ],
                            ),
                            
                            // Only show spacing if title exists
                            if (widget.note.title.isNotEmpty)
                              const SizedBox(height: 2),
                            
                            // Content preview (if available)
                            if (_contentPreview.isNotEmpty)
                              Container(
                                margin: const EdgeInsets.only(top: 8),
                                child: Text(
                                  _contentPreview,
                                  style: TextStyle(
                                    fontSize: 14,
                                    height: 1.3, // Improved line height for readability
                                    letterSpacing: 0.1, // Slight letter spacing for better legibility
                                    color: textColor.withOpacity(0.85), // Slightly soften text
                                  ),
                                  maxLines: 8, // Allow more lines for variable height
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            
                            const SizedBox(height: 2),
                            
                            // Labels
                            if (_noteLabels.isNotEmpty && !_isLoadingLabels)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 2.0),
                                child: Wrap(
                                  spacing: 2,
                                  runSpacing: 2,
                                  children: _noteLabels.map((label) => Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                                    decoration: BoxDecoration(
                                      color: label.color.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: label.color.withOpacity(0.5),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      label.name,
                                      style: TextStyle(
                                        fontSize: 10, // Smaller font
                                        color: textColor.withOpacity(0.9), // Use adaptive text color
                                      ),
                                    ),
                                  )).toList(),
                                ),
                              ),
                            
                            // Footer with date and action buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // Date - constrained to available width
                                Expanded(
                                  child: Text(
                                    DateFormatter.formatDate(widget.note.updatedAt),
                                    style: TextStyle(
                                      fontSize: 11, // Smaller font
                                      color: textColor.withOpacity(0.7), // Use adaptive text color with more transparency
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                
                                // Action buttons (if provided and not in selection mode)
                                if (!widget.isInSelectionMode && widget.actionButtons.isNotEmpty)
                                  Flexible(
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: widget.actionButtons.map((button) {
                                        // Make buttons smaller to prevent overflow
                                        return SizedBox(
                                          height: 28, // Smaller height
                                          width: 28, // Smaller width
                                          child: button,
                                        );
                                      }).toList(),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        );
                      }
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection indicator
            if (widget.isInSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: widget.isSelected
                        ? isDarkMode ? Colors.blue.shade700 : Colors.blue
                        : isDarkMode ? Colors.grey.shade800.withOpacity(0.7) : Colors.grey.shade200.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isDarkMode ? Colors.white70 : Colors.black38,
                      width: 1,
                    ),
                  ),
                  child: widget.isSelected
                      ? const Icon(
                          Icons.check,
                          size: 16,
                          color: Colors.white,
                        )
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}