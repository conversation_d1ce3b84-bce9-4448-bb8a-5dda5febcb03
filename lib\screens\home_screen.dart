import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../providers/notes_provider.dart';
import '../providers/theme_provider.dart';
import '../models/note.dart';
import '../utils/constants.dart';
import '../utils/date_formatter.dart';
import '../utils/navigation_utils.dart';
import '../utils/custom_page_transitions.dart';
import 'note_detail_screen.dart';
import 'settings_screen.dart';
import 'archive_screen.dart';
import 'trash_screen.dart';
import 'reminder_screen.dart';
import 'dart:convert';
import 'archive_lock_screen.dart';
import '../providers/labels_provider.dart';
import '../models/label.dart';
// Removed unused import
import '../widgets/label_drawer_section.dart';
import '../widgets/draggable_notes_grid.dart';
import '../widgets/draggable_notes_list.dart';
import '../screens/label_filter_screen.dart';
import 'package:hugeicons/hugeicons.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Track current view mode (list or grid)
  bool _isGridView = true;
  
  // Multi-select mode state
  bool _isMultiSelectMode = false;
  final Set<int> _selectedNoteIds = {};

  @override
  void initState() {
    super.initState();
    // Fetch notes when the screen is initialized
    Future.microtask(
        () => Provider.of<NotesProvider>(context, listen: false).fetchNotes());
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Toggle selection of a note
  void _toggleNoteSelection(int noteId) {
    setState(() {
      if (_selectedNoteIds.contains(noteId)) {
        _selectedNoteIds.remove(noteId);
        // Exit multi-select mode if no notes are selected
        if (_selectedNoteIds.isEmpty) {
          _isMultiSelectMode = false;
        }
      } else {
        _selectedNoteIds.add(noteId);
      }
    });
  }

  // Enter multi-select mode with initial note selected
  void _enterMultiSelectMode(int initialNoteId) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedNoteIds.add(initialNoteId);
    });
  }

  // Exit multi-select mode
  void _exitMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedNoteIds.clear();
    });
  }

  // Update the navigation methods in the various sections

  // 1. Archive navigation in drawer
  void _navigateToArchive() async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    // Check if archive requires authentication
    final requiresAuth = await appLockProvider.isArchiveAuthenticationRequired();
    
    // Pop the drawer first
    Navigator.pop(context);
    
    if (requiresAuth) {
      // Show lock screen first and wait for result
      final authenticated = await Navigator.push(
        context,
        NavigationUtils.createSecureRoute(
          context: context,
          screen: const ArchiveLockScreen(),
          useCustomTransition: false, // Disable custom transition for drawer navigation
        ),
      );
      
      // Only navigate to archive if authentication was successful
      if (authenticated == true && mounted) {
        Navigator.push(
          context,
          NavigationUtils.createSecureRoute(
            context: context,
            screen: const ArchiveScreen(),
          ),
        );
      }
    } else {
      // No authentication required, navigate directly to archive
      Navigator.push(
        context,
        NavigationUtils.createSecureRoute(
          context: context,
          screen: const ArchiveScreen(),
          useCustomTransition: false, // Disable custom transition for drawer navigation
        ),
      );
    }
  }

  // 2. Trash navigation in drawer
  void _navigateToTrash() {
    Navigator.pop(context);
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: const TrashScreen(),
        useCustomTransition: false, // Disable custom transition for drawer navigation
      ),
    );
  }

  // 3. Reminders navigation in drawer
  void _navigateToReminders() {
    Navigator.pop(context);
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: const ReminderScreen(),
        useCustomTransition: false, // Disable custom transition for drawer navigation
      ),
    );
  }

  // 4. Settings navigation in drawer
  void _navigateToSettings() {
    Navigator.pop(context);
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: const SettingsScreen(),
      ),
    );
  }

  // 4. Note detail navigation
  void _navigateToNoteDetail(int noteId) {
    Navigator.push(
      context,
      NavigationUtils.createSecureRouteWithHomeSlideOut(
        context: context,
        screen: NoteDetailScreen(noteId: noteId),
      ),
    ).then((_) {
      // Refresh notes when coming back from detail screen
      Provider.of<NotesProvider>(context, listen: false).fetchNotes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isMultiSelectMode ? _buildMultiSelectAppBar() : _buildNormalAppBar(),
      drawer: _isMultiSelectMode ? null : Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          final isDarkMode = themeProvider.isDarkMode;
          final textColor = isDarkMode ? Colors.white : Colors.black87;
          final secondaryTextColor = isDarkMode ? Colors.white70 : Colors.black54;
          final dividerColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;

          // Check if we're in landscape mode
          final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
          
          return Drawer(
            backgroundColor: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFAFAFA),
            elevation: 0,
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFAFAFA),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Clean Header Design
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.fromLTRB(
                        20, 
                        isLandscape ? 12 : 20, 
                        20, 
                        isLandscape ? 12 : 20
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFAFAFA),
                        border: Border(
                          bottom: BorderSide(
                            color: dividerColor.withOpacity(0.3),
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          // App Icon with clean design
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFA726),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.grid_view_rounded,
                              size: 20,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 16),
                          // App Title
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Deep Note',
                                  style: TextStyle(
                                    color: textColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Main Navigation Section
                            const SizedBox(height: 4),
                            _buildCleanMenuTile(
                              icon: Icons.grid_view_rounded,
                              title: 'All Notes',
                              isActive: true,
                              textColor: textColor,
                              isDarkMode: isDarkMode,
                              onTap: () => Navigator.pop(context),
                            ),

                            const SizedBox(height: 10),
                            
                            // Notes Organization Section
                            _buildSectionHeader("ORGANIZE", secondaryTextColor),
                            const SizedBox(height: 2),

                            _buildCleanMenuTile(
                              icon: Icons.archive_outlined,
                              title: 'Archive',
                              textColor: textColor,
                              isDarkMode: isDarkMode,
                              onTap: _navigateToArchive,
                            ),
                            _buildCleanMenuTile(
                              icon: Icons.schedule_outlined,
                              title: 'Reminders',
                              textColor: textColor,
                              isDarkMode: isDarkMode,
                              onTap: _navigateToReminders,
                            ),
                            _buildCleanMenuTile(
                              icon: Icons.delete_outline_rounded,
                              title: 'Trash',
                              textColor: textColor,
                              isDarkMode: isDarkMode,
                              onTap: _navigateToTrash,
                            ),

                            const SizedBox(height: 10),
                            
                            // Labels Section
                            const LabelDrawerSection(),
                          
                            const SizedBox(height: 10),
                            
                            // Settings Section
                            _buildSectionHeader('SETTING', secondaryTextColor),
                            const SizedBox(height: 2),
                            Consumer<ThemeProvider>(
                              builder: (context, themeProvider, _) {
                                return _buildCleanMenuTile(
                                  icon: themeProvider.isDarkMode 
                                      ? Icons.dark_mode
                                      : Icons.light_mode,
                                  title: 'Dark Mode',
                                  textColor: textColor,
                                  isDarkMode: isDarkMode,
                                  trailing: Switch(
                                    value: themeProvider.isDarkMode,
                                    onChanged: (value) {
                                      themeProvider.setDarkMode(value);
                                    },
                                  ),
                                  onTap: () {
                                    themeProvider.toggleTheme();
                                  },
                                );
                              },
                            ),
                            _buildCleanMenuTile(
                              icon: Icons.widgets_outlined,
                              title: 'App Widget',
                              textColor: textColor,
                              isDarkMode: isDarkMode,
                              onTap: () {
                                Navigator.pop(context);
                                // TODO: Navigate to widget settings
                              },
                            ),
                            _buildCleanMenuTile(
                              icon: Icons.settings_outlined,
                              title: 'Settings',
                              textColor: textColor,
                              isDarkMode: isDarkMode,
                              onTap: _navigateToSettings,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      body: Consumer<NotesProvider>(
        builder: (context, notesProvider, _) {
          final pinnedNotes = notesProvider.pinnedNotes;
          final unpinnedNotes = notesProvider.notes;
          
          if (notesProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (pinnedNotes.isEmpty && unpinnedNotes.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.note_alt_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No notes yet',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Tap the + button to create a note',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }
          
          return CustomScrollView(
            slivers: [
              // Pinned notes section
              if (pinnedNotes.isNotEmpty) ...[
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      children: [
                        const Text(
                          'PINNED',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                            letterSpacing: 1.0,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Container(
                          height: 1,
                          width: 32,
                          color: Colors.grey.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
                _isGridView
                    ? SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: _buildGridView(pinnedNotes),
                        ),
                      )
                    : SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: _buildListView(pinnedNotes),
                        ),
                      ),
              ],
              
              // Unpinned notes section
              if (unpinnedNotes.isNotEmpty) ...[
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      children: [
                        Text(
                          pinnedNotes.isNotEmpty ? 'OTHERS' : '',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                            letterSpacing: 1.0,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Container(
                          height: 1,
                          width: 32,
                          color: Colors.grey.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
                _isGridView
                    ? SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 80.0), // Add padding for FAB
                          child: _buildGridView(unpinnedNotes),
                        ),
                      )
                    : SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 80.0), // Add padding for FAB
                          child: _buildListView(unpinnedNotes),
                        ),
                      ),
              ],
              
              // Add padding at the bottom for FAB
              if (!_isGridView)
                const SliverToBoxAdapter(
                  child: SizedBox(height: 80),
                ),
            ],
          );
        },
      ),
      floatingActionButton: Builder(
        builder: (context) {
          return Material(
            color: Theme.of(context).floatingActionButtonTheme.backgroundColor,
            elevation: 6.0,
            shape: const CircleBorder(),
            child: InkWell(
              customBorder: const CircleBorder(),
              onTap: () {
                // Get the position of the FAB for animation origin
                final RenderBox fabRenderBox = context.findRenderObject() as RenderBox;
                final fabPosition = fabRenderBox.localToGlobal(Offset.zero);
                final fabSize = fabRenderBox.size;
                final fabCenter = Offset(
                  fabPosition.dx + fabSize.width / 2,
                  fabPosition.dy + fabSize.height / 2,
                );
                
                Navigator.push(
                  context,
                  CustomPageTransitions.createFabToScreenTransition(
                    context: context,
                    screen: const NoteDetailScreen(),
                    startOffset: fabCenter,
                  ),
                );
              },
              child: const SizedBox(
                width: 56,
                height: 56,
                child: Center(
                  child: Icon(Icons.add, color: Colors.white),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showNoteOptions(Note note) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final noteColor = note.themeColor != null
        ? Color(int.parse(note.themeColor!))
        : AppColors.accent;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Small handle at top of sheet
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Note title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                note.title.isEmpty ? 'Untitled Note' : note.title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: noteColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Option: Edit
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Note'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  NavigationUtils.createSecureRoute(
                    context: context,
                    screen: NoteDetailScreen(noteId: note.id),
                  ),
                );
              },
            ),
            
            // Option: Archive
            ListTile(
              leading: const Icon(Icons.archive_outlined),
              title: const Text('Archive Note'),
              onTap: () async {
                Navigator.pop(context);
                await Provider.of<NotesProvider>(context, listen: false)
                    .archiveNote(note.id!);
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Note archived')),
                  );
                }
              },
            ),
            
            // Option: Delete
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('Delete Note', style: TextStyle(color: Colors.red)),
              onTap: () async {
                Navigator.pop(context);
                await Provider.of<NotesProvider>(context, listen: false)
                    .moveToTrash(note.id!);
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Note moved to trash')),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 4),
      child: Text(
        title,
        style: TextStyle(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildCleanMenuTile({
    required IconData icon,
    required String title,
    bool isActive = false,
    required Color textColor,
    required bool isDarkMode,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 1),
      child: ListTile(
        leading: Icon(
          icon,
          size: 20,
          color: isActive 
            ? const Color(0xFFFFA726)
            : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
            color: isActive 
              ? const Color(0xFFFFA726)
              : textColor,
          ),
        ),
        trailing: trailing,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
        dense: true,
        visualDensity: const VisualDensity(vertical: -2),
        onTap: onTap,
      ),
    );
  }

  Widget _buildEnhancedMenuTile({
    required IconData icon,
    required String title,
    bool isActive = false,
    required Color textColor,
    required bool isDarkMode,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 1),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isActive 
          ? AppColors.accent.withOpacity(0.1)
          : Colors.transparent,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            gradient: isActive 
              ? LinearGradient(
                  colors: [AppColors.accent, AppColors.accent.withOpacity(0.8)],
                )
              : null,
            color: isActive 
              ? null
              : (isDarkMode 
                  ? Colors.white.withOpacity(0.08)
                  : Colors.black.withOpacity(0.05)),
            borderRadius: BorderRadius.circular(8),
            boxShadow: isActive ? [
              BoxShadow(
                color: AppColors.accent.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ] : null,
          ),
          child: Icon(
            icon,
            size: 16,
            color: isActive 
              ? Colors.white
              : AppColors.accent,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
            color: isActive 
              ? AppColors.accent
              : textColor,
            letterSpacing: 0.1,
          ),
        ),
        trailing: trailing,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        dense: true,
        visualDensity: const VisualDensity(vertical: -2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildMenuTile({
    required IconData icon,
    required String title,
    bool isActive = false,
    Color? textColor,
    Color? inactiveColor,
    VoidCallback? onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.accent.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 20,
          color: AppColors.accent,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 24),
      dense: true,
      visualDensity: const VisualDensity(vertical: -1),
      onTap: onTap,
    );
  }

  Widget _buildLabelsList() {
    return Consumer<LabelsProvider>(
      builder: (context, labelsProvider, child) {
        if (labelsProvider.isLoading) {
          return const ListTile(
            title: Text('Loading labels...'),
          );
        }
        
        final labels = labelsProvider.labels;
        
        if (labels.isEmpty) {
          return const ListTile(
            title: Text('No labels yet'),
            dense: true,
            enabled: false,
          );
        }
        
        return Column(
          children: labels.map((label) => _buildLabelTile(label)).toList(),
        );
      },
    );
  }
  
  Widget _buildLabelTile(Label label) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: label.color,
        radius: 12,
      ),
      title: Text(label.name),
      dense: true,
      onTap: () {
        // Close the drawer
        Navigator.pop(context);
        // Navigate to the label's notes screen
        Navigator.push(
          context,
          NavigationUtils.createSecureRoute(
            context: context,
            screen: LabelFilterScreen(labelId: label.id!),
          ),
        );
      },
    );
  }

  Widget _buildGridView(List<Note> notes) {
    final bool isPinned = notes.isNotEmpty && notes.first.isPinned;
    final bool isArchived = notes.isNotEmpty && notes.first.isArchived;
    
    // Use the draggable grid for notes when not in selection mode
    return DraggableNotesGrid(
      notes: notes,
      isPinned: isPinned,
      isArchived: isArchived,
      isInSelectionMode: _isMultiSelectMode,
      selectedNoteIds: _selectedNoteIds,
      onNotePressed: (note) {
        if (_isMultiSelectMode) {
          if (note.id != null) {
            _toggleNoteSelection(note.id!);
          }
        } else {
          _navigateToNoteDetail(note.id!);
        }
      },
      onNoteLongPressed: (note) {
        if (note.id != null) {
          _enterMultiSelectMode(note.id!);
        }
      },
    );
  }
  
  Widget _buildListView(List<Note> notes) {
    final bool isPinned = notes.isNotEmpty && notes.first.isPinned;
    final bool isArchived = notes.isNotEmpty && notes.first.isArchived;
    
    // Use the draggable list for notes when not in selection mode
    return DraggableNotesList(
      notes: notes,
      isPinned: isPinned,
      isArchived: isArchived,
      isInSelectionMode: _isMultiSelectMode,
      selectedNoteIds: _selectedNoteIds,
      onNotePressed: (note) {
        if (_isMultiSelectMode) {
          if (note.id != null) {
            _toggleNoteSelection(note.id!);
          }
        } else {
          _navigateToNoteDetail(note.id!);
        }
      },
      onNoteLongPressed: (note) {
        if (note.id != null) {
          _enterMultiSelectMode(note.id!);
        }
      },
      onPinTap: (note) {
        if (note.id != null) {
          Provider.of<NotesProvider>(context, listen: false).toggleNotePin(note.id!);
        }
      },
    );
  }

  PreferredSizeWidget _buildNormalAppBar() {
    return AppBar(
      title: const Text('Deep Note'),
      centerTitle: false,
      elevation: 0,
      actions: [
        // View toggle button
        IconButton(
          icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
          tooltip:
              _isGridView ? 'Switch to list view' : 'Switch to grid view',
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            final notesProvider =
                Provider.of<NotesProvider>(context, listen: false);
            final themeProvider =
                Provider.of<ThemeProvider>(context, listen: false);

            showSearch(
              context: context,
              delegate: NotesSearchDelegate(
                notes: [...notesProvider.pinnedNotes, ...notesProvider.notes],
                isDarkMode: themeProvider.isDarkMode,
              ),
            ).then((selectedNote) {
              if (selectedNote != null) {
                Navigator.push(
                  context,
                  NavigationUtils.createSecureRoute(
                    context: context,
                    screen: NoteDetailScreen(noteId: selectedNote.id),
                  ),
                );
              }
            });
          },
        ),
      ],
    );
  }

  PreferredSizeWidget _buildMultiSelectAppBar() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return AppBar(
      backgroundColor: isDarkMode ? Colors.indigo.shade900 : Colors.indigo.shade700,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: _exitMultiSelectMode,
      ),
      title: Text('${_selectedNoteIds.length} selected'),
      elevation: 2,
      actions: [
        // Pin/Unpin
        IconButton(
          icon: HugeIcon(
            icon: HugeIcons.strokeRoundedPin,
            size: 24.0,
            color: Theme.of(context).iconTheme.color ?? Colors.grey,
          ),
          tooltip: 'Pin/Unpin',
          onPressed: _togglePinSelectedNotes,
        ),
        
        // Change color
        IconButton(
          icon: const Icon(Icons.palette_outlined),
          tooltip: 'Change color',
          onPressed: () => _showColorPicker(),
        ),
        
        // Archive
        IconButton(
          icon: const Icon(Icons.archive_outlined),
          tooltip: 'Archive',
          onPressed: _archiveSelectedNotes,
        ),
        
        // Delete
        IconButton(
          icon: const Icon(Icons.delete_outline),
          tooltip: 'Delete',
          onPressed: _deleteSelectedNotes,
        ),
        
        // More options removed as requested
      ],
    );
  }
  
  // Toggle pin status for selected notes
  void _togglePinSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    
    for (final noteId in _selectedNoteIds) {
      await notesProvider.toggleNotePin(noteId);
    }
    
    _exitMultiSelectMode();
  }
  
  // Archive selected notes
  void _archiveSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    
    for (final noteId in _selectedNoteIds) {
      await notesProvider.archiveNote(noteId);
    }
    
    _exitMultiSelectMode();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_selectedNoteIds.length} notes archived'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  // Show color picker for selected notes
  void _showColorPicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choose Note Color',
                style: AppTextStyles.heading2,
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  ...AppColors.noteColors.map((color) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _applyColorToSelectedNotes(color);
                      },
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.transparent,
                            width: 3,
                          ),
                        ),
                      ),
                    );
                  }),
                  // Option to remove color
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      _applyColorToSelectedNotes(Colors.transparent);
                    },
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: AppColors.cardBackground,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey,
                          width: 3,
                        ),
                      ),
                      child: const Icon(Icons.remove, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
  
  // Apply color to selected notes
  void _applyColorToSelectedNotes(Color color) async {
    // Convert color to string format that can be stored in the database
    final colorValue = color.value.toString();
    
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    await notesProvider.updateNotesColor(_selectedNoteIds, colorValue);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Color applied to ${_selectedNoteIds.length} notes'),
        duration: const Duration(seconds: 2),
      ),
    );
    
    _exitMultiSelectMode();
  }
  
  // Removed label and delete functionality as requested
  
  // Delete selected notes
  void _deleteSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    final count = _selectedNoteIds.length;
    
    for (final noteId in _selectedNoteIds) {
      await notesProvider.moveToTrash(noteId);
    }
    
    _exitMultiSelectMode();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$count notes moved to trash'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

// Custom SearchDelegate for Notes
class NotesSearchDelegate extends SearchDelegate<Note?> {
  final List<Note> notes;
  final bool isDarkMode;

  NotesSearchDelegate({required this.notes, required this.isDarkMode})
      : super(
          searchFieldLabel: 'Search notes',
          searchFieldStyle: TextStyle(
            color: isDarkMode ? Colors.white70 : Colors.black87,
            fontSize: 16,
          ),
        );

  // Extract plain text from Quill Delta JSON
  String _getPlainTextFromDelta(String jsonContent) {
    try {
      final List<dynamic> deltaJson = jsonDecode(jsonContent);
      String plainText = '';
      for (final op in deltaJson) {
        if (op['insert'] is String) {
          plainText += op['insert'];
        } else if (op['insert'] is Map) {
          plainText += '[attachment]';
        }
      }
      return plainText.trim();
    } catch (e) {
      return jsonContent;
    }
  }

  @override
  ThemeData appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    return theme.copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor:
            isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
        iconTheme:
            IconThemeData(color: isDarkMode ? Colors.white : Colors.black87),
        elevation: 0,
      ),
      scaffoldBackgroundColor:
          isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      inputDecorationTheme: InputDecorationTheme(
        hintStyle:
            TextStyle(color: isDarkMode ? Colors.white38 : Colors.black38),
        border: InputBorder.none,
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          if (query.isEmpty) {
            close(context, null);
          } else {
            query = '';
          }
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 80,
              color: isDarkMode ? Colors.white38 : Colors.black26,
            ),
            const SizedBox(height: 16),
            Text(
              'Search your notes',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      );
    }

    final filteredNotes = notes.where((note) {
      final titleMatch = note.title.toLowerCase().contains(query.toLowerCase());
      final contentMatch = _getPlainTextFromDelta(note.content)
          .toLowerCase()
          .contains(query.toLowerCase());
      return titleMatch || contentMatch;
    }).toList();

    if (filteredNotes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: isDarkMode ? Colors.white38 : Colors.black26,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredNotes.length,
      itemBuilder: (context, index) {
        final note = filteredNotes[index];
        final noteColor = note.themeColor != null
            ? Color(int.parse(note.themeColor!))
            : AppColors.accent;
        final contentPreview = _getPlainTextFromDelta(note.content);

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: noteColor.withOpacity(0.4),
              width: 2,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            title: Text(
              note.title.isEmpty ? 'Untitled' : note.title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (contentPreview.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    contentPreview,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Text(
                  DateFormatter.getRelativeTime(note.updatedAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.white38 : Colors.black38,
                  ),
                ),
              ],
            ),
            onTap: () {
              close(context, note);
            },
          ),
        );
      },
    );
  }
}
