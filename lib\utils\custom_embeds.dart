import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';
import '../widgets/fullscreen_image_viewer.dart';

/// Custom embed builders for the notes app
class CustomEmbedBuilders {
  /// Get custom embed builders for the editor
  static List<EmbedBuilder> get customEditorBuilders {
    // Get default builders
    final defaultBuilders = FlutterQuillEmbeds.editorBuilders();
    
    // Add our custom image builder at the beginning
    return [
      CustomImageEmbedBuilder(),
      ...defaultBuilders,
    ];
  }

  /// Get custom embed builders for web editor
  static List<EmbedBuilder> get customWebEditorBuilders {
    // Get default web builders
    final defaultWebBuilders = FlutterQuillEmbeds.editorWebBuilders();
    
    // Add our custom image builder at the beginning
    return [
      CustomImageEmbedBuilder(),
      ...defaultWebBuilders,
    ];
  }
}

/// Custom image embed builder that adds border radius to images
class CustomImageEmbedBuilder implements EmbedBuilder {
  @override
  String get key => 'image';
  
  @override
  bool get expanded => false;

  @override
  Widget build(BuildContext context, EmbedContext embedContext) {
    final node = embedContext.node;
    final imageUrl = node.value.data;
    
    // First we determine if this is a network image or a local image
    final Widget image = imageUrl.startsWith('http')
        ? _buildNetworkImage(imageUrl)
        : _buildLocalImage(imageUrl);

    // Wrap in a GestureDetector to handle taps and long press for different actions
    return Container(
      // Add margin to ensure spacing between images
      margin: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 8.0),
      child: GestureDetector(
        onTap: () => _showFullScreenImage(context, imageUrl),
        onLongPress: () => _showImageContextMenu(context, imageUrl),
        child: Hero(
          tag: imageUrl,
          child: ClipRRect(
            // Add border radius of 14
            borderRadius: BorderRadius.circular(14),
            child: image,
          ),
        ),
      ),
    );
  }
  
  // Show fullscreen image view with delete option
  void _showFullScreenImage(BuildContext context, String imageUrl) {
    // Try to find the editor controller through the context
    QuillController? controller;
    
    try {
      // Look up the widget tree for the QuillController
      final editor = context.findAncestorWidgetOfExactType<QuillEditor>();
      if (editor != null) {
        controller = editor.controller;
      }
    } catch (e) {
      // Ignore errors in finding controller
    }
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return FullscreenImageViewer(
          imageUrl: imageUrl,
          controller: controller, // Pass controller if found
        );
      },
    );
  }
  
  // Show image context menu with options
  void _showImageContextMenu(BuildContext context, String imageUrl) {
    // Try to find the editor controller through the context
    QuillController? controller;
    
    try {
      // Look up the widget tree for the QuillController
      final editor = context.findAncestorWidgetOfExactType<QuillEditor>();
      if (editor != null) {
        controller = editor.controller;
      }
    } catch (e) {
      // Ignore errors in finding controller
    }
    
    // Show a modal bottom sheet with image options
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        return Container(
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Text(
                  'Image',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ),
              // Resize option
              ListTile(
                leading: Icon(
                  Icons.photo_size_select_large,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                title: Text(
                  'Resize',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Implement resize functionality
                  // This would require additional implementation
                },
              ),
              // Copy option
              ListTile(
                leading: Icon(
                  Icons.content_copy,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                title: Text(
                  'Copy',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Implement copy functionality
                  // This could involve clipboard operations
                },
              ),
              // Remove option
              ListTile(
                leading: Icon(
                  Icons.delete_outline,
                  color: Colors.red,
                ),
                title: Text(
                  'Remove',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Delete the image if we have a controller
                  if (controller != null) {
                    _deleteImageFromDocument(context, imageUrl, controller);
                  }
                },
              ),
              // Save option
              ListTile(
                leading: Icon(
                  Icons.save_alt,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                title: Text(
                  'Save',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Implement save functionality
                  // This would require saving to device storage
                },
              ),
              // Zoom option
              ListTile(
                leading: Icon(
                  Icons.zoom_in,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                title: Text(
                  'Zoom',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Open fullscreen viewer for zoom
                  _showFullScreenImage(context, imageUrl);
                },
              ),
            ],
          ),
        );
      },
    );
  }
  
  // Helper method to delete an image from the document
  void _deleteImageFromDocument(BuildContext context, String imageUrl, QuillController controller) {
    try {
      final Delta delta = controller.document.toDelta();
      final json = delta.toJson();
      
      int? foundIndex;
      int offset = 0;
      
      // Loop through operations to find the image
      for (int i = 0; i < json.length; i++) {
        final op = json[i];
        // Check if this operation contains our image
        if (op.containsKey('insert') && 
            op['insert'] is Map && 
            op['insert'].containsKey('image') && 
            op['insert']['image'] == imageUrl) {
          foundIndex = i;
          break;
        }
        
        // Calculate offset
        if (op.containsKey('insert')) {
          if (op['insert'] is String) {
            offset += op['insert'].length as int;
          } else {
            offset += 1; // Embedded objects count as 1
          }
        }
      }
      
      // If image found, delete it
      if (foundIndex != null) {
        controller.replaceText(offset, 1, '', null);
      }
    } catch (e) {
      // Show error if something went wrong
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete image: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
  

  
  @override
  WidgetSpan buildWidgetSpan(Widget child) {
    return WidgetSpan(
      alignment: PlaceholderAlignment.middle,
      child: child,
    );
  }
  
  @override
  String toPlainText(Embed node) {
    return '[Image]';
  }

  Widget _buildNetworkImage(String url) {
    return LayoutBuilder(builder: (context, constraints) {
      // Calculate the width as 50% of the available width
      // but with a minimum width to prevent too small images
      final width = max(constraints.maxWidth * 0.5, 120.0);
      
      return SizedBox(
        width: width,
        child: Image.network(
          url,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorImage();
          },
        ),
      );
    });
  }

  Widget _buildLocalImage(String path) {
    try {
      return LayoutBuilder(builder: (context, constraints) {
        // Calculate the width as 50% of the available width
        // but with a minimum width to prevent too small images
        final width = max(constraints.maxWidth * 0.5, 120.0);
        
        return SizedBox(
          width: width,
          child: Image.file(
            File(path),
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return _buildErrorImage();
            },
          ),
        );
      });
    } catch (e) {
      return _buildErrorImage();
    }
  }

  Widget _buildErrorImage() {
    return Container(
      width: 200,
      height: 200,
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          size: 40,
          color: Colors.grey,
        ),
      ),
    );
  }
}
