import 'package:flutter/material.dart';
import '../models/note.dart';
import '../providers/notes_provider.dart';
import 'package:provider/provider.dart';
import 'shared_note_card.dart';

class DraggableNotesList extends StatefulWidget {
  final List<Note> notes;
  final Function(Note)? onNotePressed;
  final Function(Note)? onNoteLongPressed;
  final bool isInSelectionMode;
  final Set<int>? selectedNoteIds;
  final bool isPinned;
  final bool isArchived;
  final Function(Note)? onPinTap;

  const DraggableNotesList({
    Key? key,
    required this.notes,
    this.onNotePressed,
    this.onNoteLongPressed,
    this.isInSelectionMode = false,
    this.selectedNoteIds,
    this.isPinned = false,
    this.isArchived = false,
    this.onPinTap,
  }) : super(key: key);

  @override
  State<DraggableNotesList> createState() => _DraggableNotesListState();
}

class _DraggableNotesListState extends State<DraggableNotesList> with TickerProviderStateMixin {
  // Track drag target positions
  List<int> dragPositions = [];
  int? draggingItemIndex;
  bool isDragging = false;
  bool positionChanged = false;
  
  // Animation controllers
  late final AnimationController _scaleController;
  late final AnimationController _elevationController;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize dragPositions based on notes
    resetDragPositions();
    
    // Initialize animation controllers
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    _elevationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.03).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOut)
    );
    
    _elevationAnimation = Tween<double>(begin: 2.0, end: 6.0).animate(
      CurvedAnimation(parent: _elevationController, curve: Curves.easeOut)
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _elevationController.dispose();
    super.dispose();
  }
  
  @override
  void didUpdateWidget(DraggableNotesList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.notes.length != widget.notes.length ||
        !_areNotesEqual(oldWidget.notes, widget.notes)) {
      resetDragPositions();
    }
  }
  
  bool _areNotesEqual(List<Note> oldNotes, List<Note> newNotes) {
    if (oldNotes.length != newNotes.length) return false;
    
    for (int i = 0; i < oldNotes.length; i++) {
      if (oldNotes[i].id != newNotes[i].id) return false;
    }
    
    return true;
  }
  
  void resetDragPositions() {
    dragPositions = List.generate(widget.notes.length, (index) => index);
  }

  @override
  Widget build(BuildContext context) {
    final notesProvider = Provider.of<NotesProvider>(context);
    
    // Reset drag state when selection mode changes
    if (widget.isInSelectionMode) {
      isDragging = false;
      positionChanged = false;
    }
    
    return widget.isInSelectionMode
        ? _buildNonDraggableList()
        : _buildDraggableList(notesProvider);
  }
  
  Widget _buildNonDraggableList() {
    // Regular list when in selection mode
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: widget.notes.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final note = widget.notes[index];
        return SharedNoteCard(
          note: note,
          isSelected: widget.selectedNoteIds?.contains(note.id) ?? false,
          isInSelectionMode: widget.isInSelectionMode,
          showPinIcon: !widget.isArchived,
          onLongPress: widget.onNoteLongPressed != null 
              ? () => widget.onNoteLongPressed!(note) 
              : null,
          onTap: widget.onNotePressed != null
              ? () => widget.onNotePressed!(note)
              : null,
          onPinTap: widget.onPinTap != null && !widget.isArchived
              ? () => widget.onPinTap!(note)
              : null,
        );
      },
    );
  }
  
  Widget _buildDraggableList(NotesProvider notesProvider) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: widget.notes.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final originalIndex = dragPositions[index];
        final note = widget.notes[originalIndex];
        
        return LongPressDraggable<int>(
          data: originalIndex,
          delay: const Duration(milliseconds: 150),
          // Keep track of the current drag state to determine if it was a drag or just a long press
          onDragStarted: () {
            setState(() {
              draggingItemIndex = originalIndex;
              isDragging = true;
              positionChanged = false;
            });
            _scaleController.forward();
            _elevationController.forward();
          },
          onDragEnd: (details) {
            setState(() {
              // If it was a drag but position didn't change, treat it as a long press selection
              if (!positionChanged && widget.onNoteLongPressed != null && !widget.isInSelectionMode) {
                widget.onNoteLongPressed!(note);
              }
              draggingItemIndex = null;
              isDragging = false;
            });
            _scaleController.reverse();
            _elevationController.reverse();
          },
          feedback: AnimatedBuilder(
            animation: Listenable.merge([_scaleAnimation, _elevationAnimation]),
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Material(
                  elevation: _elevationAnimation.value,
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width - 32,
                    child: SharedNoteCard(
                      note: note,
                      isSelected: false,
                      isInSelectionMode: false,
                      showPinIcon: !widget.isArchived,
                      onLongPress: null,
                      onTap: null,
                      onPinTap: null,
                    ),
                  ),
                ),
              );
            },
          ),
          childWhenDragging: Opacity(
            opacity: 0.2,
            child: SharedNoteCard(
              note: note,
              isSelected: false,
              isInSelectionMode: false,
              showPinIcon: !widget.isArchived,
              onLongPress: null,
              onTap: null,
              onPinTap: null,
            ),
          ),
          onDragCompleted: () {
            // Update note positions in database
            notesProvider.reorderNotesList(
              widget.notes, 
              dragPositions,
              isPinned: widget.isPinned,
              isArchived: widget.isArchived
            );
          },
          child: DragTarget<int>(
            builder: (context, candidateData, rejectedData) {
              return SharedNoteCard(
                note: note,
                isSelected: false,
                isInSelectionMode: false,
                showPinIcon: !widget.isArchived,
                onLongPress: null,
                onTap: widget.onNotePressed != null
                    ? () => widget.onNotePressed!(note)
                    : null,
                onPinTap: widget.onPinTap != null && !widget.isArchived
                    ? () => widget.onPinTap!(note)
                    : null,
              );
            },
            onWillAccept: (data) {
              return data != null && data != originalIndex;
            },
            onAccept: (draggedIndex) {
              setState(() {
                final targetIndex = dragPositions.indexOf(originalIndex);
                final sourceIndex = dragPositions.indexOf(draggedIndex);
                
                // Reorder the list
                final element = dragPositions.removeAt(sourceIndex);
                dragPositions.insert(targetIndex, element);
                
                // Mark that position actually changed
                positionChanged = true;
              });
            },
          ),
        );
      },
    );
  }
}
