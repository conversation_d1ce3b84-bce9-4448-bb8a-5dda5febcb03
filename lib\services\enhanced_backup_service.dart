import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

import '../models/note.dart';
import 'database_service.dart';

class EnhancedBackupService {
  static final EnhancedBackupService _instance = EnhancedBackupService._internal();
  final DatabaseService _databaseService = DatabaseService();

  // Private constructor
  EnhancedBackupService._internal();

  // Singleton instance
  factory EnhancedBackupService() => _instance;

  // Current backup version
  static const String _backupVersion = "2.0.0";

  // Create a backup of all notes with Base64 embedded images
  Future<Map<String, dynamic>> _createEnhancedBackupData() async {
    try {
      // Get all notes
      final List<Note> notes = await _databaseService.getNotes();
      final List<Map<String, dynamic>> enhancedNotes = [];

      // Process each note to embed images as Base64
      for (Note note in notes) {
        final Map<String, dynamic> noteMap = note.toMap();
        
        // Process the content to find and embed images
        final String enhancedContent = await _embedImagesInContent(note.content);
        noteMap['content'] = enhancedContent;
        
        enhancedNotes.add(noteMap);
      }

      // Create the backup object
      return {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'notes': enhancedNotes,
        'hasEmbeddedImages': true,
      };
    } catch (e) {
      throw Exception('Failed to create enhanced backup data: $e');
    }
  }

  // Embed images in content as Base64
  Future<String> _embedImagesInContent(String content) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/images');
      
      if (!await imagesDir.exists()) {
        return content; // No images directory, return original content
      }

      String processedContent = content;
      
      // Find all image file paths in the content
      final RegExp imagePathRegex = RegExp(r'"([^"]*\/images\/[^"]+\.(jpg|jpeg|png|gif|bmp|webp))"', caseSensitive: false);
      final Iterable<RegExpMatch> matches = imagePathRegex.allMatches(content);
      
      for (RegExpMatch match in matches) {
        final String? imagePath = match.group(1);
        if (imagePath != null) {
          final File imageFile = File(imagePath);
          
          if (await imageFile.exists()) {
            try {
              // Read image file as bytes
              final List<int> imageBytes = await imageFile.readAsBytes();
              
              // Convert to Base64
              final String base64Image = base64Encode(imageBytes);
              
              // Get file extension for MIME type
              final String extension = path.extension(imagePath).toLowerCase();
              final String mimeType = _getMimeType(extension);
              
              // Create data URL
              final String dataUrl = 'data:$mimeType;base64,$base64Image';
              
              // Replace the file path with the data URL in content
              processedContent = processedContent.replaceAll('"$imagePath"', '"$dataUrl"');
              
              if (kDebugMode) {
                print('Embedded image: ${path.basename(imagePath)} (${imageBytes.length} bytes)');
              }
            } catch (e) {
              if (kDebugMode) {
                print('Error embedding image $imagePath: $e');
              }
            }
          }
        }
      }
      
      return processedContent;
    } catch (e) {
      if (kDebugMode) {
        print('Error processing content for image embedding: $e');
      }
      return content; // Return original content on error
    }
  }

  // Get MIME type from file extension
  String _getMimeType(String extension) {
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.bmp':
        return 'image/bmp';
      case '.webp':
        return 'image/webp';
      default:
        return 'image/jpeg'; // Default fallback
    }
  }

  // Export enhanced backup to a JSON file
  Future<File?> exportEnhancedBackup() async {
    try {
      final Map<String, dynamic> backupData = await _createEnhancedBackupData();
      final String backupJson = jsonEncode(backupData);

      // Get the documents directory
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/dark_notes_enhanced_backup_$timestamp.json';

      // Write the backup to a file
      final File file = File(filePath);
      await file.writeAsString(backupJson);

      if (kDebugMode) {
        print('Enhanced backup exported: $filePath');
        print('Backup size: ${await file.length()} bytes');
      }

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('Error exporting enhanced backup: $e');
      }
      return null;
    }
  }

  // Share the enhanced backup file
  Future<bool> shareEnhancedBackup() async {
    try {
      final File? backupFile = await exportEnhancedBackup();
      if (backupFile != null) {
        await Share.shareXFiles(
          [XFile(backupFile.path)],
          subject: 'Deep Note Enhanced Backup (with Images)',
        );
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing enhanced backup: $e');
      }
      return false;
    }
  }

  // Import notes from an enhanced backup file
  Future<String> importEnhancedBackup(BuildContext context) async {
    try {
      // Open file picker
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null) {
        return "No file selected";
      }

      final file = File(result.files.single.path!);
      final contents = await file.readAsString();

      // Validate and restore the backup
      final String restoreResult = await _validateAndRestoreEnhancedBackup(contents);
      return restoreResult;
    } catch (e) {
      return "Error importing enhanced backup: $e";
    }
  }

  // Validate the enhanced backup file and restore notes
  Future<String> _validateAndRestoreEnhancedBackup(String backupJson) async {
    try {
      // Parse the backup file
      final Map<String, dynamic> backupData = jsonDecode(backupJson);

      // Validate the backup format
      if (!backupData.containsKey('version') ||
          !backupData.containsKey('timestamp') ||
          !backupData.containsKey('notes')) {
        return "Invalid backup format";
      }

      // Check if this is an enhanced backup with embedded images
      final bool hasEmbeddedImages = backupData['hasEmbeddedImages'] == true;
      
      if (kDebugMode) {
        print('Importing backup with embedded images: $hasEmbeddedImages');
      }

      // Get the notes from the backup
      final List<dynamic> notesList = backupData['notes'];

      // Clear the database
      await _databaseService.deleteAllNotes();

      int restoredImagesCount = 0;

      // Insert each note from the backup
      for (var noteMap in notesList) {
        // If this backup has embedded images, extract them
        if (hasEmbeddedImages && noteMap['content'] != null) {
          final String originalContent = noteMap['content'];
          final Map<String, String> extractedImages = await _extractImagesFromContent(originalContent);
          
          // Update content with new local file paths
          String updatedContent = originalContent;
          for (String dataUrl in extractedImages.keys) {
            final String localPath = extractedImages[dataUrl]!;
            updatedContent = updatedContent.replaceAll('"$dataUrl"', '"$localPath"');
          }
          
          noteMap['content'] = updatedContent;
          restoredImagesCount += extractedImages.length;
        }
        
        final Note note = Note.fromMap(noteMap);
        await _databaseService.insertNote(note);
      }

      String result = "Enhanced backup restored successfully";
      if (restoredImagesCount > 0) {
        result += " ($restoredImagesCount images restored)";
      }
      
      return result;
    } catch (e) {
      return "Error restoring enhanced backup: $e";
    }
  }

  // Extract Base64 images from content and save them as local files
  Future<Map<String, String>> _extractImagesFromContent(String content) async {
    final Map<String, String> extractedImages = {};
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/images');
      
      // Create images directory if it doesn't exist
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // Find all data URLs in the content
      final RegExp dataUrlRegex = RegExp(r'"(data:image\/[^;]+;base64,[^"]+)"');
      final Iterable<RegExpMatch> matches = dataUrlRegex.allMatches(content);
      
      for (RegExpMatch match in matches) {
        final String? dataUrl = match.group(1);
        if (dataUrl != null) {
          try {
            // Parse the data URL
            final RegExp parseRegex = RegExp(r'data:image\/([^;]+);base64,(.+)');
            final RegExpMatch? parseMatch = parseRegex.firstMatch(dataUrl);
            
            if (parseMatch != null) {
              final String imageType = parseMatch.group(1)!;
              final String base64Data = parseMatch.group(2)!;
              
              // Decode Base64 to bytes
              final List<int> imageBytes = base64Decode(base64Data);
              
              // Generate unique filename
              final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
              final String extension = _getExtensionFromMimeType(imageType);
              final String fileName = '${timestamp}_restored$extension';
              final String filePath = '${imagesDir.path}/$fileName';
              
              // Write the image file
              final File imageFile = File(filePath);
              await imageFile.writeAsBytes(imageBytes);
              
              // Store the mapping from data URL to local path
              extractedImages[dataUrl] = filePath;
              
              if (kDebugMode) {
                print('Extracted image: $fileName (${imageBytes.length} bytes)');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('Error extracting image from data URL: $e');
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting images from content: $e');
      }
    }
    
    return extractedImages;
  }

  // Get file extension from MIME type
  String _getExtensionFromMimeType(String mimeType) {
    switch (mimeType.toLowerCase()) {
      case 'jpeg':
        return '.jpg';
      case 'png':
        return '.png';
      case 'gif':
        return '.gif';
      case 'bmp':
        return '.bmp';
      case 'webp':
        return '.webp';
      default:
        return '.jpg'; // Default fallback
    }
  }

  // Check if a backup file has embedded images
  Future<bool> hasEmbeddedImages(String backupJson) async {
    try {
      final Map<String, dynamic> backupData = jsonDecode(backupJson);
      return backupData['hasEmbeddedImages'] == true;
    } catch (e) {
      return false;
    }
  }

  // Get backup file info
  Future<Map<String, dynamic>?> getBackupInfo(String backupJson) async {
    try {
      final Map<String, dynamic> backupData = jsonDecode(backupJson);
      final List<dynamic> notesList = backupData['notes'] ?? [];
      
      return {
        'version': backupData['version'],
        'timestamp': backupData['timestamp'],
        'notesCount': notesList.length,
        'hasEmbeddedImages': backupData['hasEmbeddedImages'] == true,
      };
    } catch (e) {
      return null;
    }
  }
}