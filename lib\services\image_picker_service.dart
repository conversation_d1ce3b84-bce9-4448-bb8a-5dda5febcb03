import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
// Import for google_mlkit_text_recognition (ensure dependency is added to pubspec.yaml)
// import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class ImagePickerService {
  static final ImagePicker _picker = ImagePicker();

  /// Shows a bottom sheet with options to pick image, get from link, or extract text.
  static Future<Map<String, dynamic>?> showImageSourceDialog(BuildContext context) async {
    return showModalBottomSheet<Map<String, dynamic>?>(
      context: context,
      backgroundColor: Theme.of(context).bottomSheetTheme.modalBackgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext sheetContext) { // Renamed to avoid conflict
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8.0), // Add some bottom padding
            child: Wrap(
              children: [
                // Optional: Drag handle
                Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[400],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.image_outlined), // Matching screenshot style
                  title: const Text('Gallery'),
                  subtitle: const Text('Pick a photo from your gallery'),
                  onTap: () async {
                    final file = await _pickImageFromGallery(sheetContext);
                    if (file != null) {
                      Navigator.pop(sheetContext, {'action': 'gallery', 'file': file});
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt_outlined), // Matching screenshot style
                  title: const Text('Camera'),
                  subtitle: const Text('Take a photo using your camera'),
                  onTap: () async {
                    final file = await _pickImageFromCamera(sheetContext);
                    if (file != null) {
                      Navigator.pop(sheetContext, {'action': 'camera', 'file': file});
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.link), // Matching screenshot style
                  title: const Text('Link'),
                  subtitle: const Text('Paste a photo using a link'),
                  onTap: () {
                    Navigator.pop(sheetContext, {'action': 'link'});
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // _buildImageSourceOption is removed as ListTiles are used directly.

  /// Pick image from gallery specifically for OCR, with permission handling.
  static Future<File?> pickImageForOcr(BuildContext context) async {
    // This method largely mirrors _pickImageFromGallery but is public for direct use.
    // In the future, it could have different logic if OCR needs specific image sources.
    return _pickImageFromGallery(context);
  }


  /// Pick image from camera with permission handling
  static Future<File?> _pickImageFromCamera(BuildContext context) async {
    try {
      // Check camera permission
      final cameraStatus = await Permission.camera.status;
      if (cameraStatus.isDenied) {
        final result = await Permission.camera.request();
        if (result.isDenied) {
          _showPermissionDeniedDialog(context, 'Camera');
          return null;
        }
      }

      if (cameraStatus.isPermanentlyDenied) {
        _showPermissionPermanentlyDeniedDialog(context, 'Camera');
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1920,
      );

      if (image != null) {
        return File(image.path);
      }
    } catch (e) {
      _showErrorDialog(context, 'Failed to take photo: $e');
    }
    return null;
  }

  /// Pick image from gallery with permission handling
  static Future<File?> _pickImageFromGallery(BuildContext context) async {
    try {
      // Check storage permission (for Android)
      if (Platform.isAndroid) {
        final storageStatus = await Permission.storage.status;
        if (storageStatus.isDenied) {
          final result = await Permission.storage.request();
          if (result.isDenied) {
            // Try photos permission for Android 13+
            final photosStatus = await Permission.photos.status;
            if (photosStatus.isDenied) {
              final photosResult = await Permission.photos.request();
              if (photosResult.isDenied) {
                _showPermissionDeniedDialog(context, 'Storage');
                return null;
              }
            }
          }
        }

        if (storageStatus.isPermanentlyDenied) {
          _showPermissionPermanentlyDeniedDialog(context, 'Storage');
          return null;
        }
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1920,
      );

      if (image != null) {
        return File(image.path);
      }
    } catch (e) {
      _showErrorDialog(context, 'Failed to select image: $e');
    }
    return null;
  }

  /// Show permission denied dialog
  static void _showPermissionDeniedDialog(BuildContext context, String permission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$permission Permission Required'),
        content: Text(
          'This app needs $permission permission to add images to your notes. Please grant the permission and try again.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show permission permanently denied dialog
  static void _showPermissionPermanentlyDeniedDialog(BuildContext context, String permission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$permission Permission Required'),
        content: Text(
          '$permission permission is permanently denied. Please enable it in app settings to add images to your notes.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}