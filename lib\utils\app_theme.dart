import 'package:flutter/material.dart';
import 'constants.dart';

/// Simplified theme implementation to avoid laggy transitions
class AppTheme {
  // Core colors
  static const Color primaryColor = Color(0xFF303F9F);
  static const Color accentColor = AppColors.accent;
  
  // Dark theme colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  
  // Light theme colors
  static const Color lightBackground = Color(0xFFF5F5F5);
  static const Color lightSurface = Colors.white;
  
  // Create a single theme that adapts based on brightness
  static ThemeData createTheme(bool isDark) {
    // Base colors based on theme
    final backgroundColor = isDark ? darkBackground : lightBackground;
    final surfaceColor = isDark ? darkSurface : lightSurface;
    final textColor = isDark ? Colors.white : Colors.black87;
    final secondaryTextColor = isDark ? Colors.white70 : Colors.black54;
    final dividerColor = isDark ? Colors.white12 : Colors.black12;
    
    return ThemeData(
      useMaterial3: true,
      brightness: isDark ? Brightness.dark : Brightness.light,
      primaryColor: primaryColor,
      colorScheme: isDark 
        ? ColorScheme.dark(
            primary: primaryColor,
            secondary: accentColor,
            surface: surfaceColor,
            background: backgroundColor,
            error: Colors.redAccent,
          )
        : ColorScheme.light(
            primary: primaryColor,
            secondary: accentColor,
            surface: surfaceColor,
            background: backgroundColor,
            error: Colors.redAccent,
          ),
      scaffoldBackgroundColor: backgroundColor,
      cardColor: surfaceColor,
      dialogBackgroundColor: surfaceColor,
      appBarTheme: AppBarTheme(
        backgroundColor: backgroundColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: textColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(color: textColor),
      ),
      iconTheme: IconThemeData(
        color: textColor,
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(color: textColor),
        displayMedium: TextStyle(color: textColor),
        displaySmall: TextStyle(color: textColor),
        headlineLarge: TextStyle(color: textColor),
        headlineMedium: TextStyle(color: textColor),
        headlineSmall: TextStyle(color: textColor),
        titleLarge: TextStyle(color: textColor),
        titleMedium: TextStyle(color: textColor),
        titleSmall: TextStyle(color: textColor),
        bodyLarge: TextStyle(color: textColor),
        bodyMedium: TextStyle(color: textColor),
        bodySmall: TextStyle(color: secondaryTextColor),
        labelLarge: TextStyle(color: textColor),
        labelMedium: TextStyle(color: textColor),
        labelSmall: TextStyle(color: secondaryTextColor),
      ),
      dividerTheme: DividerThemeData(
        color: dividerColor,
        thickness: 1,
      ),
      cardTheme: CardTheme(
        color: surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: accentColor,
        foregroundColor: Colors.white,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return accentColor;
          }
          return isDark ? Colors.grey[400] : Colors.grey[50];
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return accentColor.withOpacity(0.5);
          }
          return isDark ? Colors.grey[700] : Colors.grey[300];
        }),
      ),
      listTileTheme: ListTileThemeData(
        iconColor: textColor,
        textColor: textColor,
      ),
    );
  }
}
