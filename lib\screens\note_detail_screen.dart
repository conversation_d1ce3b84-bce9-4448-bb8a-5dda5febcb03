import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import '../utils/color_utils.dart';
import '../models/note.dart';
import '../providers/notes_provider.dart';
import '../providers/labels_provider.dart';
import '../utils/constants.dart';
import '../utils/date_formatter.dart';
import '../models/label.dart';
import '../widgets/label_bottom_sheet.dart';
import '../services/image_picker_service.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class NoteDetailScreen extends StatefulWidget {
  final int? noteId;
  final String? heroTag;

  const NoteDetailScreen({super.key, this.noteId, this.heroTag});

  @override
  State<NoteDetailScreen> createState() => _NoteDetailScreenState();
}

class _NoteDetailScreenState extends State<NoteDetailScreen> {
  late QuillController _quillController;
  final TextEditingController _titleController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  Note? _existingNote;
  bool _isLoading = false;
  String? _selectedThemeColor;
  bool _hasReminder = false;
  DateTime? _reminderTime;

  // Auto-save
  Timer? _autoSaveTimer;
  bool _hasUnsavedChanges = false;
  final int _autoSaveDelayMilliseconds = 1500; // 1.5 seconds delay

  List<Label> _selectedLabels = [];
  
  // Store provider references
  late NotesProvider _notesProvider;
  late LabelsProvider _labelsProvider;

  @override
  void initState() {
    super.initState();
    _quillController = QuillController.basic();

    // Get provider references early
    _notesProvider = Provider.of<NotesProvider>(context, listen: false);
    _labelsProvider = Provider.of<LabelsProvider>(context, listen: false);

    // Load note data if editing an existing note
    if (widget.noteId != null) {
      _loadNote();
    } else {
      // Set a timer to start auto-saving
      _startAutoSaveTimer();
    }
    _quillController.document.changes.listen((_) {
      _onContentChanged();
    });

    _loadNote();
  }

  // Image picker callback methods
  Future<String?> _onImagePickCallback(File file) async {
    // Copy the image to app's documents directory
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final newPath = '${appDir.path}/images/$fileName';
      
      // Create images directory if it doesn't exist
      final imagesDir = Directory('${appDir.path}/images');
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }
      
      // Copy the file
      final newFile = await file.copy(newPath);
      return newFile.path;
    } catch (e) {
      print('Error copying image: $e');
      return null;
    }
  }

  Future<String?> _filePickImpl(BuildContext context) async {
    final result = await ImagePickerService.showImageSourceDialog(context);

    if (result == null) return null;

    final String? action = result['action'] as String?;
    final File? file = result['file'] as File?;

    switch (action) {
      case 'gallery':
      case 'camera':
        if (file != null) {
          return await _onImagePickCallback(file);
        }
        break;
      case 'link':
        // TODO: Implement image insertion from URL
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Image from Link: Coming soon!')),
        );
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Unknown action.')),
        );
    }
    return null; // No image path to return for link or OCR actions directly to Quill embed
  }

  Future<void> _performOcrAndInsertText(File imageFile) async {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Extracting text from image...')), 
    );

    try {
      final inputImage = InputImage.fromFilePath(imageFile.path);
      final textRecognizer = TextRecognizer();
      final RecognizedText recognizedText = await textRecognizer.processImage(inputImage);
      await textRecognizer.close();

      if (recognizedText.text.isNotEmpty) {
        final index = _quillController.selection.baseOffset;
        final length = _quillController.selection.extentOffset - index;
        _quillController.document.insert(index, recognizedText.text);
        // Optionally, move cursor to the end of inserted text
        _quillController.updateSelection(
          TextSelection.collapsed(offset: index + recognizedText.text.length),
          ChangeSource.local,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Text extracted and inserted.')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No text found in the image.')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error extracting text: $e')),
      );
      print('OCR Error: $e');
    }
  }

  Future<String?> _webImagePickImpl(BuildContext context) async {
    // For web implementation, you might want to handle differently
    // For now, we'll use the same implementation
    return await _filePickImpl(context);
  }

  @override
  void dispose() {
    // Cancel timer if active
    _autoSaveTimer?.cancel();

    // Save any unsaved changes before disposing
    if (_hasUnsavedChanges) {
      _saveNote(skipNotifyListeners: true);
    }

    _quillController.dispose();
    _titleController.dispose();
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    _hasUnsavedChanges = true;
    _startAutoSaveTimer();
  }

  void _startAutoSaveTimer() {
    // Cancel any existing timer
    _autoSaveTimer?.cancel();

    // Start a new timer
    _autoSaveTimer =
        Timer(Duration(milliseconds: _autoSaveDelayMilliseconds), () {
      if (_hasUnsavedChanges) {
        _saveNote();
        _hasUnsavedChanges = false;
      }
    });
  }

  Future<void> _loadNote() async {
    if (widget.noteId != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final note = await _notesProvider.getNote(widget.noteId!);

        if (note != null) {
          _existingNote = note;
          _titleController.text = note.title;

          // Load content into QuillController
          if (note.content.isNotEmpty) {
            try {
              // Try to parse as JSON Delta
              final document = Document.fromJson(jsonDecode(note.content));
              _quillController = QuillController(
                document: document,
                selection: const TextSelection.collapsed(offset: 0),
              );

              // Re-attach listener after replacing the controller
              _quillController.document.changes.listen((_) {
                _onContentChanged();
              });
            } catch (e) {
              // If not a valid Delta, set as plain text
              final document = Document()..insert(0, note.content);
              _quillController = QuillController(
                document: document,
                selection: const TextSelection.collapsed(offset: 0),
              );

              // Re-attach listener after replacing the controller
              _quillController.document.changes.listen((_) {
                _onContentChanged();
              });
            }
          }

          _selectedThemeColor = note.themeColor;
          _hasReminder = note.hasReminder;
          _reminderTime = note.reminderTime;
          
          // Load labels for the note
          if (note.id != null) {
            _loadLabelsForNote(note.id!);
          }
        }
      } catch (e) {
        // Handle error (show snackbar, etc.)
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load note')),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadLabelsForNote(int noteId) async {
    final labels = await _labelsProvider.getLabelsForNote(noteId);
    
    setState(() {
      _selectedLabels = labels;
    });
  }

  Future<void> _saveNote({bool skipNotifyListeners = false}) async {
    final title = _titleController.text.trim();
    final String contentJson =
        jsonEncode(_quillController.document.toDelta().toJson());

    // Skip saving if both title and content are empty
    if (title.isEmpty && _quillController.document.isEmpty()) {
      return;
    }

    final now = DateTime.now();

    // Get reminderRepeatType from existing note or default to none
    final reminderRepeatType = _existingNote?.reminderRepeatType ?? ReminderRepeatType.none;

    final Note note = _existingNote != null
        ? _existingNote!.copyWith(
            title: title,
            content: contentJson,
            updatedAt: now,
            themeColor: _selectedThemeColor,
            hasReminder: _hasReminder,
            reminderTime: _reminderTime,
            reminderRepeatType: reminderRepeatType,
          )
        : Note(
            title: title,
            content: contentJson,
            createdAt: now,
            updatedAt: now,
            themeColor: _selectedThemeColor,
            hasReminder: _hasReminder,
            reminderTime: _reminderTime,
            reminderRepeatType: reminderRepeatType,
          );

    try {
      if (_existingNote != null) {
        await _notesProvider.updateNote(note);
        // Update the existing note with the saved version
        _existingNote = note;
      } else {
        final id = await _notesProvider.addNote(note);
        if (id > 0) {
          // Update this note as existing after first save
          _existingNote = note.copyWith(id: id);
          
          // Add labels to the newly created note
          for (final label in _selectedLabels) {
            if (label.id != null) {
              await _labelsProvider.addLabelToNote(id, label.id!);
            }
          }
        }
      }
    } catch (e) {
      // Handle error (show snackbar, etc.)
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save note')),
        );
      }
    }
  }

  void _deleteNote() {
    if (_existingNote != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Delete Note'),
          content: const Text('Are you sure you want to delete this note?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog
                if (_existingNote != null) {
                  _notesProvider.moveToTrash(_existingNote!.id!)
                      .then((_) {
                    Navigator.pop(context); // Go back to previous screen
                  });
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
    }
  }

  void _setReminderTime() async {
    final initialTime =
        _reminderTime ?? DateTime.now().add(const Duration(minutes: 15));

    // Get the current orientation
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    // For landscape mode, we'll use a more compact approach with a custom dialog
    if (isLandscape) {
      _showDateTimePickerDialog(initialTime);
      return;
    }

    // Default flow for portrait mode
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialTime,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDate != null) {
      final pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialTime),
      );

      if (pickedTime != null) {
        // Create the reminder time
        final selectedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
        
        // Show the repeat options dialog
        _showRepeatOptionsDialog(selectedDateTime);
      }
    }
  }
  
  // Custom date time picker for landscape mode
  void _showDateTimePickerDialog(DateTime initialTime) {
    DateTime selectedDate = initialTime;
    TimeOfDay selectedTime = TimeOfDay.fromDateTime(initialTime);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              backgroundColor: isDarkMode ? AppColors.cardBackground : Colors.white,
              elevation: 8,
              child: Container(
                width: double.maxFinite,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                  maxWidth: 400,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header with accent color
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.alarm,
                            size: 32,
                            color: Colors.white,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Set Reminder",
                            style: AppTextStyles.heading2.copyWith(
                              color: Colors.white,
                              fontSize: 22,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    
                    // Content
                    Flexible(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Date selection
                              Container(
                                margin: const EdgeInsets.symmetric(vertical: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode ? Colors.black26 : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
                                    width: 1,
                                  ),
                                ),
                                child: ListTile(
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: AppColors.accent.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.calendar_today,
                                      color: AppColors.accent,
                                      size: 20,
                                    ),
                                  ),
                                  title: Text(
                                    "Date",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: isDarkMode ? Colors.white : Colors.black87,
                                    ),
                                  ),
                                  subtitle: Text(
                                    "${selectedDate.day}/${selectedDate.month}/${selectedDate.year}",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: isDarkMode ? Colors.white70 : Colors.black54,
                                    ),
                                  ),
                                  onTap: () async {
                                    final pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: selectedDate,
                                      firstDate: DateTime.now(),
                                      lastDate: DateTime.now().add(const Duration(days: 365)),
                                    );
                                    
                                    if (pickedDate != null) {
                                      setState(() {
                                        selectedDate = DateTime(
                                          pickedDate.year,
                                          pickedDate.month,
                                          pickedDate.day,
                                          selectedDate.hour,
                                          selectedDate.minute,
                                        );
                                      });
                                    }
                                  },
                                ),
                              ),
                              
                              const SizedBox(height: 16),
                              
                              // Time selection
                              Container(
                                margin: const EdgeInsets.symmetric(vertical: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode ? Colors.black26 : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
                                    width: 1,
                                  ),
                                ),
                                child: ListTile(
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: AppColors.accent.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.access_time,
                                      color: AppColors.accent,
                                      size: 20,
                                    ),
                                  ),
                                  title: Text(
                                    "Time",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: isDarkMode ? Colors.white : Colors.black87,
                                    ),
                                  ),
                                  subtitle: Text(
                                    "${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: isDarkMode ? Colors.white70 : Colors.black54,
                                    ),
                                  ),
                                  onTap: () async {
                                    final pickedTime = await showTimePicker(
                                      context: context,
                                      initialTime: selectedTime,
                                    );
                                    
                                    if (pickedTime != null) {
                                      setState(() {
                                        selectedTime = pickedTime;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    // Action buttons
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: AppColors.accent,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            ),
                            child: Text(
                              'Cancel',
                              style: AppTextStyles.button.copyWith(
                                color: isDarkMode ? Colors.white70 : Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () {
                              // Create the final DateTime
                              final selectedDateTime = DateTime(
                                selectedDate.year,
                                selectedDate.month,
                                selectedDate.day,
                                selectedTime.hour,
                                selectedTime.minute,
                              );
                              
                              Navigator.of(context).pop();
                              
                              // Show the repeat options dialog
                              _showRepeatOptionsDialog(selectedDateTime);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.accent,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Next',
                              style: AppTextStyles.button.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Add a new method to show the repeat options dialog
  void _showRepeatOptionsDialog(DateTime selectedDateTime) {
    ReminderRepeatType selectedRepeatType = _existingNote?.reminderRepeatType ?? ReminderRepeatType.none;
    
    // Get the current orientation and theme
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, dialogSetState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              backgroundColor: isDarkMode ? AppColors.cardBackground : Colors.white,
              elevation: 8,
              child: Container(
                width: double.maxFinite,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * (isLandscape ? 0.5 : 0.7),
                  maxWidth: 400,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header with accent color
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.repeat,
                            size: 32,
                            color: Colors.white,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Repeat Reminder",
                            style: AppTextStyles.heading2.copyWith(
                              color: Colors.white,
                              fontSize: 22,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    
                    // Content
                    Flexible(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                child: Text(
                                  "How often should this reminder repeat?",
                                  style: AppTextStyles.subtitle.copyWith(
                                    color: isDarkMode ? Colors.white : Colors.black87,
                                    fontSize: isLandscape ? 14 : 16,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              
                              // Radio options with custom styling
                              ...ReminderRepeatType.values.map((type) {
                                final isSelected = selectedRepeatType == type;
                                return InkWell(
                                  onTap: () {
                                    dialogSetState(() {
                                      selectedRepeatType = type;
                                    });
                                  },
                                  borderRadius: BorderRadius.circular(8),
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                    decoration: BoxDecoration(
                                      color: isSelected 
                                          ? AppColors.accent.withOpacity(isDarkMode ? 0.2 : 0.1)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: isSelected ? AppColors.accent : Colors.transparent,
                                        width: 1,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 24,
                                            height: 24,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: isSelected ? AppColors.accent : Colors.transparent,
                                              border: Border.all(
                                                color: isSelected ? AppColors.accent : (isDarkMode ? Colors.white54 : Colors.black45),
                                                width: 2,
                                              ),
                                            ),
                                            child: isSelected
                                                ? const Icon(
                                                    Icons.check,
                                                    size: 16,
                                                    color: Colors.white,
                                                  )
                                                : null,
                                          ),
                                          const SizedBox(width: 16),
                                          Text(
                                            DateFormatter.formatRepeatType(type),
                                            style: TextStyle(
                                              fontSize: isLandscape ? 14 : 16,
                                              color: isDarkMode ? Colors.white : Colors.black87,
                                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    // Action buttons
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop(); // Close the dialog without saving
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: AppColors.accent,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            ),
                            child: Text(
                              'Cancel',
                              style: AppTextStyles.button.copyWith(
                                color: isDarkMode ? Colors.white70 : Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () {
                              // Update the parent widget's state to display the reminder immediately
                              setState(() {
                                _reminderTime = selectedDateTime;
                                _hasReminder = true;
                                // Save the repeat type
                                if (_existingNote != null) {
                                  _existingNote = _existingNote!.copyWith(
                                    reminderRepeatType: selectedRepeatType,
                                  );
                                }

                                // Trigger save when reminder is set
                                _hasUnsavedChanges = true;
                              });
                              
                              // Close the dialog first
                              Navigator.of(context).pop();
                              
                              // Save the note after a short delay to avoid state issues
                              Future.microtask(() {
                                if (mounted) {
                                  _saveNote();
                                }
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.accent,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Save',
                              style: AppTextStyles.button.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        );
      },
    );
  }

  void _showColorPicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choose Note Color',
                style: AppTextStyles.heading2,
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  ...AppColors.noteColors.map((color) {
                    final colorValue = color.value.toString();
                    final isSelected = _selectedThemeColor == colorValue;

                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedThemeColor = colorValue;
                        });
                        Navigator.pop(context);

                        // Trigger save when color is changed
                        _hasUnsavedChanges = true;
                        _saveNote();
                      },
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color:
                                isSelected ? Colors.white : Colors.transparent,
                            width: 3,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(Icons.check, color: Colors.white)
                            : null,
                      ),
                    );
                  }),
                  // Option to remove color
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedThemeColor = null;
                      });
                      Navigator.pop(context);

                      // Trigger save when color is removed
                      _hasUnsavedChanges = true;
                      _saveNote();
                    },
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: AppColors.cardBackground,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _selectedThemeColor == null
                              ? Colors.white
                              : Colors.grey,
                          width: 3,
                        ),
                      ),
                      child: const Icon(Icons.remove, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLabelSelector() {
    LabelBottomSheet.show(
      context,
      noteId: _existingNote?.id,
      selectedLabels: _selectedLabels,
      onLabelsChanged: (labels) {
        setState(() {
          _selectedLabels = labels;
        });
      },
    );
  }

  void _handleMenuItemSelected(String value) async {
    switch (value) {
      case 'archive':
        if (_existingNote != null && _existingNote!.id != null) {
          await _notesProvider.archiveNote(_existingNote!.id!);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Note archived')),
            );
            Navigator.pop(context);
          }
        }
        break;
      case 'unarchive':
        if (_existingNote != null && _existingNote!.id != null) {
          await _notesProvider.unarchiveNote(_existingNote!.id!);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Note unarchived')),
            );
            Navigator.pop(context);
          }
        }
        break;
      case 'delete':
        _deleteNote();
        break;
      case 'share':
        // Implement share functionality
        break;
    }
  }
  
  void _showReminderDialog() {
    // Implement reminder dialog
    _setReminderTime();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Get background color with proper adaptation for readability
    Color backgroundColor;
    
    if (_selectedThemeColor != null) {
      final originalColor = Color(int.parse(_selectedThemeColor!));
      backgroundColor = ColorUtils.getBackgroundVersion(originalColor, isDarkTheme: isDarkMode);
    } else {
      backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    }
    
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: _selectedThemeColor != null 
            ? ColorUtils.getBackgroundVersion(Color(int.parse(_selectedThemeColor!)), isDarkTheme: isDarkMode)
            : null,
        foregroundColor: _selectedThemeColor != null 
            ? ColorUtils.getTextColorForBackground(
                ColorUtils.getBackgroundVersion(Color(int.parse(_selectedThemeColor!)), isDarkTheme: isDarkMode)
              )
            : null,
        actions: [
          // Pin/Unpin button
          if (_existingNote != null)
            IconButton(
              icon: HugeIcon(
                icon: HugeIcons.strokeRoundedPin,
                color: _existingNote!.isPinned 
                    ? isDarkMode ? Colors.amber : Colors.orange.shade700
                    : Theme.of(context).iconTheme.color ?? Colors.grey,
                size: 24.0,
              ),
              tooltip: _existingNote!.isPinned ? 'Unpin note' : 'Pin note',
              onPressed: _togglePin,
            ),
          // Reminder button
          IconButton(
            icon: Icon(_hasReminder ? Icons.notifications_active : Icons.notifications_none),
            onPressed: _showReminderDialog,
          ),
          // Color picker button
          IconButton(
            icon: const Icon(Icons.color_lens_outlined),
            onPressed: _showColorPicker,
          ),
          // Labels button
          IconButton(
            icon: const Icon(Icons.label_outline),
            onPressed: _showLabelSelector,
          ),
          // More options
          PopupMenuButton<String>(
            onSelected: _handleMenuItemSelected,
            itemBuilder: (BuildContext context) {
              return [
                if (_existingNote != null && !_existingNote!.isArchived)
                  const PopupMenuItem<String>(
                    value: 'archive',
                    child: ListTile(
                      leading: Icon(Icons.archive_outlined),
                      title: Text('Archive'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                if (_existingNote != null && _existingNote!.isArchived)
                  const PopupMenuItem<String>(
                    value: 'unarchive',
                    child: ListTile(
                      leading: Icon(Icons.unarchive_outlined),
                      title: Text('Unarchive'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                if (_existingNote != null)
                  const PopupMenuItem<String>(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete_outline),
                      title: Text('Delete'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                const PopupMenuItem<String>(
                  value: 'share',
                  child: ListTile(
                    leading: Icon(Icons.share_outlined),
                    title: Text('Share'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ];
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildNoteEditor(),
    );
  }

  // Toggle pin status
  void _togglePin() async {
    if (_existingNote != null && _existingNote!.id != null) {
      await _notesProvider.toggleNotePin(_existingNote!.id!);
      
      // Update local state
      setState(() {
        _existingNote = _existingNote!.copyWith(isPinned: !_existingNote!.isPinned);
      });
    }
  }
  
  // Custom method to build only the image embed button, removing video options
  List<EmbedButtonBuilder> _buildImageOnlyEmbedButtons() {
    // Return only the image embed buttons, filtering out video buttons
    return FlutterQuillEmbeds.toolbarButtons()
      .where((button) => 
        // Keep only image-related buttons, filter out video and camera buttons
        button.runtimeType.toString().contains('Image') && 
        !button.runtimeType.toString().contains('Video'))
      .toList();
  }

  Widget _buildNoteEditor() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Get adaptive colors for better readability
    Color backgroundColor;
    Color textColor;
    Color placeholderColor;
    
    if (_selectedThemeColor != null) {
      final originalColor = Color(int.parse(_selectedThemeColor!));
      backgroundColor = ColorUtils.getBackgroundVersion(originalColor, isDarkTheme: isDarkMode);
      textColor = ColorUtils.getTextColorForBackground(backgroundColor);
      placeholderColor = textColor.withOpacity(0.5);
    } else {
      backgroundColor = Theme.of(context).scaffoldBackgroundColor;
      textColor = isDarkMode ? Colors.white : Colors.black87;
      placeholderColor = isDarkMode ? AppColors.textSecondary : Colors.grey[600]!;
    }
    
    return Column(
      children: [
        // Simple text input for title with no borders or decorations
        Container(
          color: Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          width: double.infinity,
          child: Stack(
            children: [
              // Placeholder text that shows when there's no content
              if (_titleController.text.isEmpty)
                Text(
                  'Title',
                  style: AppTextStyles.heading1.copyWith(
                    color: placeholderColor,
                  ),
                ),
              // Actual editable text field
              EditableText(
                controller: _titleController,
                focusNode: _titleFocusNode,
                style: AppTextStyles.heading1.copyWith(
                  color: textColor,
                ),
                cursorColor: Theme.of(context).primaryColor,
                backgroundCursorColor: Colors.transparent,
                maxLines: 1,
                textCapitalization: TextCapitalization.sentences,
                onChanged: (value) {
                  // Trigger state update to show/hide placeholder
                  setState(() {});
                  _onContentChanged();
                },
                selectionControls: MaterialTextSelectionControls(),
                keyboardType: TextInputType.text,
                strutStyle: const StrutStyle(),
                inputFormatters: [],
                textDirection: TextDirection.ltr,
                readOnly: false,
                autofocus: false,
                showCursor: true,
                enableInteractiveSelection: true,
                showSelectionHandles: true,
                textAlign: TextAlign.left,
                minLines: 1,
                rendererIgnoresPointer: false,
                obscureText: false,
              ),
            ],
          ),
        ),

        // Display labels and reminder in a clean container section
        if (_selectedLabels.isNotEmpty || (_hasReminder && _reminderTime != null))
          Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display selected labels in a horizontal row
                  if (_selectedLabels.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      alignment: Alignment.centerLeft,
                      width: double.infinity,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: _selectedLabels.map((label) => 
                            Container(
                              margin: const EdgeInsets.only(right: 8),
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                              decoration: BoxDecoration(
                                color: label.color.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CircleAvatar(
                                    backgroundColor: label.color,
                                    radius: 6,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    "#${label.name}",
                                    style: TextStyle(
                                      color: label.color,
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ).toList(),
                        ),
                      ),
                    ),
                  
                  // Reminder info
                  if (_hasReminder && _reminderTime != null)
                    Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.notifications_active,
                            size: 18,
                            color: AppColors.accent,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Reminder: ${DateFormatter.formatReminder(_reminderTime!)}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.accent,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (_existingNote?.reminderRepeatType != null && 
                                    _existingNote!.reminderRepeatType != ReminderRepeatType.none)
                                  Text(
                                    'Every ${DateFormatter.formatRepeatType(_existingNote!.reminderRepeatType).toLowerCase()}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: AppColors.accent.withOpacity(0.8),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _hasReminder = false;
                                _reminderTime = null;
                                if (_existingNote != null) {
                                  _existingNote = _existingNote!.copyWith(
                                    reminderRepeatType: ReminderRepeatType.none,
                                  );
                                }

                                // Trigger save when reminder is removed
                                _hasUnsavedChanges = true;
                              });
                              
                              // Save the note
                              _saveNote();
                            },
                            child: Icon(
                              Icons.close,
                              size: 18,
                              color: AppColors.accent.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

        // Divider with proper styling
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            height: 1,
            color: Theme.of(context).dividerColor.withOpacity(0.2),
          ),
        ),

        const SizedBox(height: 8),

        // Editor with adaptive colors for better text visibility
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            // Using theme styling with our color adaptation
            child: Theme(
              // Apply adaptive text colors based on background
              data: Theme.of(context).copyWith(
                textTheme: Theme.of(context).textTheme.copyWith(
                  // Set body text color based on background for better readability
                  bodyMedium: TextStyle(
                    color: _selectedThemeColor != null
                        ? ColorUtils.getTextColorForBackground(backgroundColor)
                        : (isDarkMode ? Colors.white : Colors.black87),
                    fontSize: 16.0,
                    height: 1.3,
                  ),
                ),
                // Apply hint text color for placeholder
                hintColor: _selectedThemeColor != null
                    ? ColorUtils.getTextColorForBackground(backgroundColor).withOpacity(0.5)
                    : (isDarkMode ? Colors.white60 : Colors.black54),
              ),
              child: QuillEditor(
                controller: _quillController,
                focusNode: _contentFocusNode,
                scrollController: ScrollController(),
                config: QuillEditorConfig(
                  padding: const EdgeInsets.all(0),
                  placeholder: 'Start writing...',
                  embedBuilders: FlutterQuillEmbeds.editorBuilders(),
                ),
              ),
            ),
          ),
        ),

        // Toolbar at bottom
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF121212) // Match dark theme scaffold
                : Colors.grey[50], // Match light theme scaffold
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 2,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: QuillSimpleToolbar(
            controller: _quillController,
            config: QuillSimpleToolbarConfig(
              // Enable font family selection and text color
              showFontFamily: true,
              showFontSize: false,
              
              // Keep text color but remove background color/highlighter
              showColorButton: true,  // Text color
              showBackgroundColorButton: false,  // Background highlighter
              
              // Remove advanced formatting
              showStrikeThrough: false,
              showCodeBlock: false,
              showQuote: false,
              showInlineCode: false,
              showSubscript: false,
              showSuperscript: false,
              
              // Remove layout controls
              showIndent: false,
              showDirection: false,
              showAlignmentButtons: false,
              
              // Use compact single-row layout
              multiRowsDisplay: false,
              
              // Only include image button, remove video options
              embedButtons: _buildImageOnlyEmbedButtons(),
              customButtons: [
                QuillToolbarCustomButtonOptions(
                  icon: Icon(Icons.document_scanner_outlined),
                  tooltip: 'Image to Text',
                  onPressed: () async {
                    final file = await ImagePickerService.pickImageForOcr(context);
                    if (file != null) {
                      _performOcrAndInsertText(file);
                    }
                  },
                ),
              ]
            ),
          ),
        ),
      ],
    );
  }
}
