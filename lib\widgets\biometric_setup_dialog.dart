import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../utils/constants.dart';

/// A modern dialog for verifying PIN before enabling biometric authentication
class BiometricSetupDialog extends StatefulWidget {
  final Function(String) onPinSubmitted;
  final Function() onCancel;

  const BiometricSetupDialog({
    Key? key,
    required this.onPinSubmitted,
    required this.onCancel,
  }) : super(key: key);

  @override
  State<BiometricSetupDialog> createState() => _BiometricSetupDialogState();
}

class _BiometricSetupDialogState extends State<BiometricSetupDialog>
    with TickerProviderStateMixin {
  final TextEditingController _pinController = TextEditingController();
  final FocusNode _pinFocusNode = FocusNode();
  bool _isPinError = false;
  String _errorMessage = '';
  late AnimationController _scaleController;
  late AnimationController _shakeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticOut,
    ));
    
    // Start entrance animation
    _scaleController.forward();
    
    // Request focus on PIN field when dialog opens
    Future.microtask(() => _pinFocusNode.requestFocus());
  }

  @override
  void dispose() {
    _pinController.dispose();
    _pinFocusNode.dispose();
    _scaleController.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  void _onPinSubmitted(String pin) {
    if (pin.length == 4) {
      Navigator.of(context).pop();
      widget.onPinSubmitted(pin);
    } else {
      setState(() {
        _isPinError = true;
        _errorMessage = 'Please enter your 4-digit PIN';
      });
      _shakeController.forward().then((_) {
        _shakeController.reset();
      });
    }
  }

  Widget _buildCancelButton(bool isDarkMode, bool isTablet) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: isDarkMode 
              ? Colors.grey[600]! 
              : Colors.grey[300]!,
          width: 1.5,
        ),
      ),
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          widget.onCancel();
        },
        style: TextButton.styleFrom(
          foregroundColor: isDarkMode 
              ? Colors.grey[400] 
              : Colors.grey[600],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: const Text(
          'Cancel',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton(bool isTablet) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          colors: [
            AppColors.accent,
            AppColors.accent.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.accent.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextButton(
        onPressed: () => _onPinSubmitted(_pinController.text),
        style: TextButton.styleFrom(
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: const Text(
          'Confirm',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Get screen size and orientation for responsive adjustments
    final screenSize = MediaQuery.of(context).size;
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;
    final isSmallScreen = screenSize.height < 600 || screenSize.width < 400;
    final isTablet = screenSize.shortestSide >= 600;
    
    // Calculate responsive values based on screen size and orientation
    final iconSize = isTablet ? 100.0 : (isSmallScreen ? 64.0 : 80.0);
    final titleFontSize = isTablet ? 28.0 : (isSmallScreen ? 20.0 : 24.0);
    final contentFontSize = isTablet ? 18.0 : (isSmallScreen ? 14.0 : 16.0);
    final verticalSpacing = isLandscape ? 12.0 : (isSmallScreen ? 16.0 : 24.0);
    final horizontalPadding = isTablet ? 40.0 : (isSmallScreen ? 20.0 : 28.0);
    
    // Calculate PIN field dimensions
    final fieldSize = isTablet ? 70.0 : (isSmallScreen ? 50.0 : 60.0);
    
    // Adjust dialog width based on screen size
    final dialogWidth = isTablet ? 480.0 : (isSmallScreen ? 320.0 : 380.0);
    final maxDialogHeight = isLandscape ? screenSize.height * 0.9 : screenSize.height * 0.7;
    
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: dialogWidth,
            maxHeight: maxDialogHeight,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode
                  ? [
                      const Color(0xFF2A2A2A).withOpacity(0.95),
                      const Color(0xFF1E1E1E).withOpacity(0.95),
                    ]
                  : [
                      Colors.white.withOpacity(0.95),
                      Colors.grey[50]!.withOpacity(0.95),
                    ],
            ),
            border: Border.all(
              color: isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: SingleChildScrollView(
              padding: EdgeInsets.all(horizontalPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Security Icon with gradient background
                  Container(
                    width: iconSize + 20,
                    height: iconSize + 20,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.accent,
                          AppColors.accent.withOpacity(0.7),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.accent.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.fingerprint,
                      size: iconSize * 0.6,
                      color: Colors.white,
                    ),
                  ),
                  
                  SizedBox(height: verticalSpacing),
                  
                  // Title
                  Text(
                    'Verify Your PIN',
                    style: TextStyle(
                      fontSize: titleFontSize,
                      fontWeight: FontWeight.w700,
                      color: isDarkMode ? Colors.white : Colors.black87,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: verticalSpacing / 2),
                  
                  // Subtitle
                  Text(
                    'Enter your PIN to enable biometric\nauthentication for enhanced security',
                    style: TextStyle(
                      fontSize: contentFontSize,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      height: 1.4,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: verticalSpacing * 1.5),
                  
                  // PIN Input with shake animation
                  AnimatedBuilder(
                    animation: _shakeAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(
                          _shakeAnimation.value * 10 * 
                              (1 - _shakeAnimation.value) * 
                              ((_shakeAnimation.value * 4).floor() % 2 == 0 ? 1 : -1),
                          0,
                        ),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: isTablet ? 16.0 : 8.0,
                          ),
                          child: PinCodeTextField(
                            appContext: context,
                            length: 4,
                            controller: _pinController,
                            focusNode: _pinFocusNode,
                            obscureText: true,
                            autoFocus: true,
                            animationType: AnimationType.scale,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            backgroundColor: Colors.transparent,
                            keyboardType: TextInputType.number,
                            pinTheme: PinTheme(
                              shape: PinCodeFieldShape.circle,
                              fieldHeight: fieldSize,
                              fieldWidth: fieldSize,
                              activeFillColor: AppColors.accent.withOpacity(0.1),
                              activeColor: AppColors.accent,
                              inactiveColor: isDarkMode 
                                  ? Colors.grey[600] 
                                  : Colors.grey[400],
                              selectedColor: AppColors.accent,
                              selectedFillColor: AppColors.accent.withOpacity(0.15),
                              inactiveFillColor: isDarkMode 
                                  ? Colors.grey[800]!.withOpacity(0.3)
                                  : Colors.grey[100],
                              borderWidth: 2,
                              errorBorderColor: Colors.red[400],
                            ),
                            cursorColor: AppColors.accent,
                            animationDuration: const Duration(milliseconds: 300),
                            enableActiveFill: true,
                            onCompleted: _onPinSubmitted,
                            onChanged: (value) {
                              if (_isPinError) {
                                setState(() {
                                  _isPinError = false;
                                  _errorMessage = '';
                                });
                              }
                            },
                          ),
                        ),
                      );
                    },
                  ),
                  
                  // Error message
                  if (_isPinError)
                    Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.red[200]!,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 16,
                              color: Colors.red[600],
                            ),
                            const SizedBox(width: 6),
                            Text(
                              _errorMessage,
                              style: TextStyle(
                                color: Colors.red[600],
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  SizedBox(height: verticalSpacing * 1.5),
                  
                  // Responsive button layout
                  isLandscape
                      ? Row(
                          children: [
                            Expanded(
                              child: _buildCancelButton(isDarkMode, isTablet),
                            ),
                            SizedBox(width: isTablet ? 20 : 16),
                            Expanded(
                              child: _buildConfirmButton(isTablet),
                            ),
                          ],
                        )
                      : Column(
                          children: [
                            _buildCancelButton(isDarkMode, isTablet),
                            SizedBox(height: isTablet ? 16 : 12),
                            _buildConfirmButton(isTablet),
                          ],
                        ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}