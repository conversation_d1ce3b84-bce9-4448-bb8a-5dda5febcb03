import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider with ChangeNotifier {
  static const String _isDarkModeKey = 'is_dark_mode';
  bool _isDarkMode = true; // Default to dark mode
  
  // Constructor initializes from shared preferences
  ThemeProvider() {
    _loadThemePreference();
  }
  
  // Getter for current theme state
  bool get isDarkMode => _isDarkMode;
  
  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    _isDarkMode = prefs.getBool(_isDarkModeKey) ?? true;
    notifyListeners();
  }
  
  // Toggle the theme
  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isDarkModeKey, _isDarkMode);
    
    notifyListeners();
  }
  
  // Set specific theme mode
  Future<void> setDarkMode(bool darkMode) async {
    if (_isDarkMode == darkMode) return;
    
    _isDarkMode = darkMode;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isDarkModeKey, _isDarkMode);
    
    notifyListeners();
  }
}
