import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../screens/label_management_screen.dart';
import '../screens/label_filter_screen.dart';

class LabelDrawerSection extends StatelessWidget {
  const LabelDrawerSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final secondaryTextColor = isDarkMode ? Colors.white70 : Colors.black54;
    
    return Consumer<LabelsProvider>(
      builder: (context, labelsProvider, child) {
        final visibleLabels = labelsProvider.visibleLabels;
        
        if (visibleLabels.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced section header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
              child: Text(
                'LABELS',
                style: TextStyle(
                  color: secondaryTextColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w700,
                  letterSpacing: 1.2,
                ),
              ),
            ),
            const SizedBox(height: 2),
            ...visibleLabels.map((label) => _buildEnhancedLabelTile(context, label, labelsProvider, isDarkMode)),
            // Enhanced add label management tile
            _buildAddLabelTile(context, isDarkMode),
          ],
        );
      },
    );
  }

  Widget _buildEnhancedLabelTile(BuildContext context, Label label, LabelsProvider labelsProvider, bool isDarkMode) {
    final labelCount = labelsProvider.getLabelCount(label.id!);
    final isSelected = labelsProvider.selectedLabelIds.contains(label.id);
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 0.5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isSelected 
          ? label.color.withOpacity(0.1)
          : Colors.transparent,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: label.color.withOpacity(0.15),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: label.color.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.label_rounded,
            size: 14,
            color: label.color,
          ),
        ),
        title: Text(
          label.name,
          style: TextStyle(
            fontSize: 13,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? label.color : textColor,
            letterSpacing: 0.1,
          ),
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: isDarkMode 
              ? Colors.white.withOpacity(0.1)
              : Colors.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            '$labelCount',
            style: TextStyle(
              color: isDarkMode ? Colors.white70 : Colors.black54,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
        dense: true,
        visualDensity: const VisualDensity(vertical: -2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        onTap: () {
          Navigator.pop(context); // Close drawer
          
          if (isSelected) {
            // If already selected, clear selection
            labelsProvider.clearSelectedLabels();
          } else {
            // Otherwise navigate to label filter screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => LabelFilterScreen(labelId: label.id!),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildAddLabelTile(BuildContext context, bool isDarkMode) {
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final accentColor = Theme.of(context).colorScheme.primary;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 0.5),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: accentColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.add_rounded,
            size: 14,
            color: accentColor,
          ),
        ),
        title: Text(
          'Manage Labels',
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: textColor,
            letterSpacing: 0.1,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
        dense: true,
        visualDensity: const VisualDensity(vertical: -2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        onTap: () {
          Navigator.pop(context); // Close drawer
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const LabelManagementScreen(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLabelTile(BuildContext context, Label label, LabelsProvider labelsProvider) {
    final labelCount = labelsProvider.getLabelCount(label.id!);
    final isSelected = labelsProvider.selectedLabelIds.contains(label.id);
    
    return ListTile(
      dense: true,
      selected: isSelected,
      leading: Icon(
        Icons.label_outline,
        size: 20,
        color: label.color,
      ),
      title: Text(label.name),
      trailing: Text(
        '$labelCount',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      onTap: () {
        Navigator.pop(context); // Close drawer
        
        if (isSelected) {
          // If already selected, clear selection
          labelsProvider.clearSelectedLabels();
        } else {
          // Otherwise navigate to label filter screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => LabelFilterScreen(labelId: label.id!),
            ),
          );
        }
      },
    );
  }

  void _showLabelManagementDialog(BuildContext context) {
    // Navigate to a full-screen labels page instead of showing a dialog
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LabelManagementScreen(),
      ),
    );
  }
}


