import 'package:flutter/material.dart';
import 'dart:io';
import 'package:flutter/services.dart';
import '../utils/constants.dart';

/// A modern, responsive dialog that guides users to set up biometrics at the OS level
class BiometricGuidanceDialog extends StatelessWidget {
  final Function()? onDismiss;

  const BiometricGuidanceDialog({
    Key? key,
    this.onDismiss,
  }) : super(key: key);
  
  // Method to open device settings
  Future<void> _openDeviceSettings() async {
    try {
      if (Platform.isAndroid) {
        // For Android, we can try to open the biometric settings directly
        await SystemNavigator.pop();
        
        // Note: This is a workaround, as Flutter doesn't provide direct access
        // to open specific system settings without using plugins
        // After system navigator pop, the user needs to go to settings manually
      } else if (Platform.isIOS) {
        // For iOS, this will open the device settings
        await SystemNavigator.pop();
      }
    } catch (e) {
      // Handle any errors that might occur
      debugPrint('Error opening settings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Get screen size and orientation for responsive adjustments
    final screenSize = MediaQuery.of(context).size;
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;
    final isSmallScreen = screenSize.height < 600 || screenSize.width < 400;
    final isTablet = screenSize.shortestSide >= 600;
    
    // Calculate responsive values
    final iconSize = isTablet ? 100.0 : (isSmallScreen ? 64.0 : 80.0);
    final titleFontSize = isTablet ? 28.0 : (isSmallScreen ? 20.0 : 24.0);
    final contentFontSize = isTablet ? 18.0 : (isSmallScreen ? 14.0 : 16.0);
    final stepFontSize = isTablet ? 16.0 : (isSmallScreen ? 13.0 : 14.0);
    final verticalSpacing = isLandscape ? 12.0 : (isSmallScreen ? 16.0 : 24.0);
    final horizontalPadding = isTablet ? 40.0 : (isSmallScreen ? 20.0 : 28.0);
    
    // Adjust dialog width based on screen size
    final dialogWidth = isTablet ? 520.0 : (isSmallScreen ? 340.0 : 420.0);
    final maxDialogHeight = isLandscape ? screenSize.height * 0.9 : screenSize.height * 0.8;
    
    // Show different instructions based on platform
    final String instructionTitle = Platform.isIOS 
        ? 'Set Up Face ID/Touch ID'
        : 'Set Up Fingerprint/Face Unlock';
    
    final List<String> instructionSteps = Platform.isIOS
        ? [
            'Open Settings',
            'Tap Face ID & Passcode (or Touch ID & Passcode)',
            'Enter your device passcode',
            'Set up Face ID or Touch ID',
            'Follow the on-screen instructions to register your biometrics'
          ]
        : [
            'Open Settings',
            'Tap Security & privacy',
            'Tap Fingerprint or Face unlock',
            'Enter your PIN/password if prompted',
            'Follow the on-screen instructions to register your fingerprint/face'
          ];
    
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: dialogWidth,
          maxHeight: maxDialogHeight,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDarkMode
                ? [
                    const Color(0xFF2A2A2A),
                    const Color(0xFF1E1E1E),
                  ]
                : [
                    Colors.white,
                    const Color(0xFFF8F9FA),
                  ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(isDarkMode ? 0.6 : 0.15),
              blurRadius: 24,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: SingleChildScrollView(
            padding: EdgeInsets.all(horizontalPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: verticalSpacing),
                
                // Icon with gradient background
                Container(
                  width: iconSize + 20,
                  height: iconSize + 20,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.accent,
                        AppColors.accent.withOpacity(0.7),
                      ],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.accent.withOpacity(0.3),
                        blurRadius: 16,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.fingerprint_rounded,
                    size: iconSize,
                    color: Colors.white,
                  ),
                ),
                
                SizedBox(height: verticalSpacing),
                
                // Title
                Text(
                  'Biometric Setup Required',
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
                  ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: verticalSpacing * 0.5),
                
                // Subtitle
                Text(
                  'You need to register your fingerprint/face in your device settings before using biometric authentication for enhanced security',
                  style: TextStyle(
                    fontSize: contentFontSize,
                    color: isDarkMode ? Colors.white70 : Colors.grey[600],
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: verticalSpacing * 1.5),
                
                // Instructions section
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
                  decoration: BoxDecoration(
                    color: isDarkMode 
                        ? Colors.white.withOpacity(0.05)
                        : Colors.grey[50],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isDarkMode 
                          ? Colors.white.withOpacity(0.1)
                          : Colors.grey[200]!,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        instructionTitle,
                        style: TextStyle(
                          fontSize: contentFontSize + 2,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
                        ),
                      ),
                      SizedBox(height: verticalSpacing * 0.75),
                      ...instructionSteps.asMap().entries.map((entry) {
                        final index = entry.key;
                        final step = entry.value;
                        return Padding(
                          padding: EdgeInsets.only(
                            bottom: index < instructionSteps.length - 1 ? 12.0 : 0,
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: AppColors.accent,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    '${index + 1}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  step,
                                  style: TextStyle(
                                    fontSize: stepFontSize,
                                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
                
                SizedBox(height: verticalSpacing),
                
                // Info banner
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(isTablet ? 20.0 : 16.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.accent.withOpacity(0.1),
                        AppColors.accent.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.accent.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline_rounded,
                        size: isTablet ? 24 : 20,
                        color: AppColors.accent,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'After registering your biometrics, return to Deep Note to complete the setup.',
                          style: TextStyle(
                            fontSize: stepFontSize,
                            color: isDarkMode ? Colors.white.withOpacity(0.9) : Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(height: verticalSpacing * 1.5),
                
                // Action buttons - responsive layout
                isLandscape
                    ? Row(
                        children: [
                          Expanded(
                            child: _buildOpenSettingsButton(context, isDarkMode, isTablet),
                          ),
                          SizedBox(width: isTablet ? 20 : 16),
                          Expanded(
                            child: _buildLaterButton(context, isDarkMode, isTablet),
                          ),
                        ],
                      )
                    : Column(
                        children: [
                          _buildOpenSettingsButton(context, isDarkMode, isTablet),
                          SizedBox(height: isTablet ? 16 : 12),
                          _buildLaterButton(context, isDarkMode, isTablet),
                        ],
                      ),
                
                SizedBox(height: verticalSpacing * 0.5),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildOpenSettingsButton(BuildContext context, bool isDarkMode, bool isTablet) {
    return Container(
      width: double.infinity,
      height: 52,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.accent,
            AppColors.accent.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(26),
        boxShadow: [
          BoxShadow(
            color: AppColors.accent.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextButton.icon(
        onPressed: () async {
          Navigator.pop(context);
          await _openDeviceSettings();
          if (onDismiss != null) {
            onDismiss!();
          }
        },
        style: TextButton.styleFrom(
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(26),
          ),
        ),
        icon: const Icon(
          Icons.settings_rounded,
          size: 20,
        ),
        label: Text(
          'Open Settings',
          style: TextStyle(
            fontSize: isTablet ? 18 : 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
  
  Widget _buildLaterButton(BuildContext context, bool isDarkMode, bool isTablet) {
    return Container(
      width: double.infinity,
      height: 52,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(26),
        border: Border.all(
          color: isDarkMode 
              ? Colors.grey[600]! 
              : Colors.grey[300]!,
          width: 1.5,
        ),
        color: isDarkMode 
            ? Colors.white.withOpacity(0.05)
            : Colors.white,
      ),
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          if (onDismiss != null) {
            onDismiss!();
          }
        },
        style: TextButton.styleFrom(
          foregroundColor: isDarkMode 
              ? Colors.grey[300] 
              : Colors.grey[700],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(26),
          ),
        ),
        child: Text(
          'I\'ll Do It Later',
          style: TextStyle(
            fontSize: isTablet ? 18 : 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}