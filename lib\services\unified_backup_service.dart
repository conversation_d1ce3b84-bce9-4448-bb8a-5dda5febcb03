import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

import '../models/note.dart';
import 'database_service.dart';
import '../providers/app_lock_provider.dart';
import '../utils/constants.dart';
import '../utils/crypto_utils.dart';

class UnifiedBackupService {
  static final UnifiedBackupService _instance = UnifiedBackupService._internal();
  final DatabaseService _databaseService = DatabaseService();

  // Private constructor
  UnifiedBackupService._internal();

  // Singleton instance
  factory UnifiedBackupService() => _instance;

  // Current backup version
  static const String _backupVersion = "1.0.0";

  // Create a backup of all notes
  Future<Map<String, dynamic>> _createBackupData() async {
    try {
      // Get all notes
      final List<Note> notes = await _databaseService.getNotes();

      // Create the backup object
      return {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'notes': notes.map((note) => note.toMap()).toList(),
      };
    } catch (e) {
      throw Exception('Failed to create backup data: $e');
    }
  }

  // Export regular unencrypted backup to a JSON file
  Future<File?> _exportBackup() async {
    try {
      final Map<String, dynamic> backupData = await _createBackupData();
      final String backupJson = jsonEncode(backupData);

      // Get the documents directory
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/dark_notes_backup_$timestamp.json';

      // Write the backup to a file
      final File file = File(filePath);
      await file.writeAsString(backupJson);

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('Error exporting backup: $e');
      }
      return null;
    }
  }

  // Create an encrypted backup using the provided password
  Future<File?> _exportEncryptedBackup(String password) async {
    try {
      final Map<String, dynamic> backupData = await _createBackupData();
      final String backupJson = jsonEncode(backupData);
      
      // Generate a secure salt and derive key from password
      final salt = CryptoUtils.generateSalt();
      final derivedKey = CryptoUtils.deriveKey(password, salt);
      final key = encrypt.Key(derivedKey);
      final iv = encrypt.IV(CryptoUtils.generateIV());
      
      // Encrypt the backup data
      final encrypter = encrypt.Encrypter(encrypt.AES(key));
      final encryptedData = encrypter.encrypt(backupJson, iv: iv);
      
      // Create the encrypted backup format
      final encryptedBackup = {
        'version': _backupVersion,
        'isEncrypted': true,
        'salt': salt,
        'iv': iv.base64,
        'data': encryptedData.base64,
      };
      
      final String encryptedJson = jsonEncode(encryptedBackup);

      // Get the documents directory
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/dark_notes_secure_backup_$timestamp.json';

      // Write the backup to a file
      final File file = File(filePath);
      await file.writeAsString(encryptedJson);

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('Error exporting encrypted backup: $e');
      }
      return null;
    }
  }

  // Unified backup method with authentication challenge
  Future<bool> createBackup(
    BuildContext context, 
    bool encrypt, 
    AppLockProvider appLockProvider,
    {String? oneTimePassword}
  ) async {
    try {
      File? backupFile;
      
      if (encrypt) {
        String? password;
        
        // If app lock is enabled, use that as the encryption password
        if (appLockProvider.isLockEnabled) {
          // This will be filled by the PIN verification dialog
          password = await _promptForPinVerification(context, appLockProvider);
          if (password == null) {
            // User cancelled the PIN verification
            return false;
          }
        } else if (oneTimePassword != null && oneTimePassword.isNotEmpty) {
          // Use the provided one-time password if no app lock
          password = oneTimePassword;
        } else {
          // No password available
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Password required for encrypted backup')),
          );
          return false;
        }
        
        backupFile = await _exportEncryptedBackup(password);
      } else {
        // Regular unencrypted backup
        backupFile = await _exportBackup();
      }
      
      if (backupFile != null) {
        await Share.shareXFiles(
          [XFile(backupFile.path)],
          subject: 'Deep Note Backup',
        );
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating backup: $e');
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to create backup: $e')),
      );
      return false;
    }
  }

  // Prompt the user to verify their PIN before proceeding
  Future<String?> _promptForPinVerification(
    BuildContext context, 
    AppLockProvider appLockProvider
  ) async {
    // This will store the verified PIN
    String? verifiedPin;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Show PIN verification dialog
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Theme(
        data: Theme.of(context).copyWith(
          dialogBackgroundColor: isDarkMode ? AppColors.cardBackground : Colors.white,
        ),
        child: AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text(
            'Verify PIN',
            style: AppTextStyles.heading2.copyWith(
              color: isDarkMode ? AppColors.textPrimary : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
              Text(
                'Enter your app lock PIN to encrypt the backup',
                style: AppTextStyles.body.copyWith(
                  color: isDarkMode ? AppColors.textSecondary : Colors.black54,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
            TextField(
              autofocus: true,
              obscureText: true,
              keyboardType: TextInputType.number,
              maxLength: 6,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isDarkMode ? AppColors.textPrimary : Colors.black87,
                  fontSize: 18,
                  letterSpacing: 8,
                ),
                decoration: InputDecoration(
                hintText: 'PIN',
                counterText: '',
                  filled: true,
                  fillColor: isDarkMode ? Colors.grey[900] : Colors.grey[50],
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode ? AppColors.accent.withOpacity(0.5) : Colors.grey[300]!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: AppColors.accent,
                      width: 2,
                    ),
                  ),
              ),
              onChanged: (value) {
                // Store the entered PIN temporarily
                verifiedPin = value;
              },
            ),
          ],
        ),
        actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
            onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.accent,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
          ),
                Expanded(
                  child: TextButton(
            onPressed: () async {
              if (verifiedPin != null && verifiedPin!.isNotEmpty) {
                // Verify the entered PIN
                final isValid = await appLockProvider.authenticateWithPin(verifiedPin!);
                if (isValid) {
                  Navigator.pop(context, true); // PIN verified successfully
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Incorrect PIN')),
                  );
                }
              }
            },
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.accent,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Verify',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
          ),
        ],
            ),
          ],
        ),
      ),
    ).then((result) {
      // If the dialog was dismissed without verification, reset the PIN
      if (result != true) {
        verifiedPin = null;
      }
    });
    
    return verifiedPin;
  }

  // Import notes from a backup file with unified authentication
  Future<String> restoreBackup(
    BuildContext context, 
    AppLockProvider appLockProvider,
    {String? oneTimePassword}
  ) async {
    try {
      // Open file picker
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null) {
        return "No file selected";
      }

      final file = File(result.files.single.path!);
      final contents = await file.readAsString();
      
      // Parse the backup
      final Map<String, dynamic> parsedData = jsonDecode(contents);
      
      // Check if it's an encrypted backup
      final bool isEncrypted = parsedData.containsKey('isEncrypted') && parsedData['isEncrypted'] == true;
      
      if (isEncrypted) {
        String? password;
        
        // If app lock is enabled, use that for decryption
        if (appLockProvider.isLockEnabled) {
          password = await _promptForPinVerification(context, appLockProvider);
          if (password == null) {
            return "PIN verification cancelled";
          }
        } else if (oneTimePassword != null && oneTimePassword.isNotEmpty) {
          password = oneTimePassword;
        } else {
          // No password available
          return "Password required to decrypt this backup";
        }
        
        try {
          // Attempt to decrypt the backup
          final String decryptedContents = _decryptBackup(parsedData, password);
          return await _validateAndRestoreBackup(decryptedContents);
        } catch (e) {
          return "Failed to decrypt backup: Incorrect password or corrupted file";
        }
      } else {
        // Regular unencrypted backup
        return await _validateAndRestoreBackup(contents);
      }
    } catch (e) {
      return "Error importing backup: $e";
    }
  }
  
  // Decrypt an encrypted backup
  String _decryptBackup(Map<String, dynamic> encryptedData, String password) {
    // Extract the salt, IV and encrypted data
    final String? saltString = encryptedData['salt'];
    final String ivString = encryptedData['iv'];
    final String encryptedString = encryptedData['data'];
    
    // Generate a key from the password
    encrypt.Key key;
    if (saltString != null) {
      // New format with proper key derivation
      final salt = base64.decode(saltString);
      final derivedKey = CryptoUtils.deriveKey(password, salt);
      key = encrypt.Key(derivedKey);
    } else {
      // Legacy format for backward compatibility
      key = encrypt.Key.fromUtf8(password.padRight(32, '0').substring(0, 32));
    }
    
    final iv = encrypt.IV.fromBase64(ivString);
    
    // Decrypt the data
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    final decryptedData = encrypter.decrypt64(encryptedString, iv: iv);
    
    return decryptedData;
  }

  // Validate the backup file and restore notes
  Future<String> _validateAndRestoreBackup(String backupJson) async {
    try {
      // Parse the backup file
      final Map<String, dynamic> backupData = jsonDecode(backupJson);

      // Validate the backup format
      if (!backupData.containsKey('version') ||
          !backupData.containsKey('timestamp') ||
          !backupData.containsKey('notes')) {
        return "Invalid backup format";
      }

      // Check version compatibility
      final String backupVersion = backupData['version'];
      if (backupVersion != _backupVersion) {
        // For simplicity, just log it, but in a real app you would need version migration logic
        if (kDebugMode) {
          print(
              'Warning: Backup version ($backupVersion) differs from current version ($_backupVersion)');
        }
      }

      // Get the notes from the backup
      final List<dynamic> notesList = backupData['notes'];

      // Clear the database
      await _databaseService.deleteAllNotes();

      // Insert each note from the backup
      for (var noteMap in notesList) {
        final Note note = Note.fromMap(noteMap);
        await _databaseService.insertNote(note);
      }

      return "Backup restored successfully";
    } catch (e) {
      return "Error restoring backup: $e";
    }
  }
}
