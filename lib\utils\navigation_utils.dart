import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_lifecycle_manager.dart';
import '../widgets/memory_optimizer.dart';
import '../screens/app_lock_screen.dart';
import '../services/memory_service.dart';

/// A utility class for navigation functions
class NavigationUtils {
  /// Creates a secure route that wraps the destination screen with AppLockScreen
  /// and other necessary providers to ensure app lock is enforced consistently.
  /// Uses right-to-left slide animation for a consistent look throughout the app.
  static Route<T> createSecureRoute<T>({
    required BuildContext context,
    required Widget screen,
    RouteSettings? settings,
    bool fullscreenDialog = false,
    bool useCustomTransition = true,
  }) {
    return _createRoute<T>(
      context: context,
      screen: screen,
      settings: settings,
      fullscreenDialog: fullscreenDialog,
      useCustomTransition: useCustomTransition,
      withHomeSlideOut: false,
    );
  }

  /// Creates a secure route with home page slide-out animation
  /// The home page slides to the left while the new screen slides in from the right
  static Route<T> createSecureRouteWithHomeSlideOut<T>({
    required BuildContext context,
    required Widget screen,
    RouteSettings? settings,
    bool fullscreenDialog = false,
    bool useCustomTransition = true,
  }) {
    return _createRoute<T>(
      context: context,
      screen: screen,
      settings: settings,
      fullscreenDialog: fullscreenDialog,
      useCustomTransition: useCustomTransition,
      withHomeSlideOut: true,
    );
  }

  /// Internal method to create routes with optional home slide-out animation
  static Route<T> _createRoute<T>({
    required BuildContext context,
    required Widget screen,
    RouteSettings? settings,
    bool fullscreenDialog = false,
    bool useCustomTransition = true,
    bool withHomeSlideOut = false,
  }) {
    // Get the memory service from the provider
    final memoryService = Provider.of<MemoryService>(context, listen: false);
    
    // Create the wrapped widget with all necessary providers
    final wrappedScreen = MemoryOptimizer(
      memoryService: memoryService,
      child: AppLifecycleManager(
        child: AppLockScreen(
          child: screen,
        ),
      ),
    );
    
    // If this is from a drawer or custom transition is disabled, use standard route
    if (!useCustomTransition || fullscreenDialog) {
      return MaterialPageRoute<T>(
        settings: settings,
        fullscreenDialog: fullscreenDialog,
        builder: (context) => wrappedScreen,
      );
    }
    
    // Use custom slide transition for all other navigation
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => wrappedScreen,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Incoming screen animation (new screen slides in from right)
        const incomingBegin = Offset(1.0, 0.0);
        const incomingEnd = Offset.zero;
        final incomingTween = Tween(begin: incomingBegin, end: incomingEnd);
        final incomingAnimation = animation.drive(incomingTween);
        
        // Add a subtle fade and scale effect for incoming screen
        final fadeAnimation = Tween<double>(begin: 0.3, end: 1.0)
          .animate(CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
          ));
        
        final scaleAnimation = Tween<double>(begin: 0.94, end: 1.0)
          .animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
          ));

        Widget incomingScreen = SlideTransition(
          position: incomingAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: child,
            ),
          ),
        );

        // If home slide-out is enabled, add secondary animation for the home page
        if (withHomeSlideOut) {
          // Home page slides out to the left
          const outgoingBegin = Offset.zero;
          const outgoingEnd = Offset(-0.3, 0.0); // Slide out partially to the left
          final outgoingTween = Tween(begin: outgoingBegin, end: outgoingEnd);
          final outgoingAnimation = secondaryAnimation.drive(outgoingTween);

          // Apply the outgoing animation to the previous screen (home page)
          return Stack(
            children: [
              // Previous screen (home page) sliding out
              SlideTransition(
                position: outgoingAnimation,
                child: Container(), // This will be the previous screen
              ),
              // New screen sliding in
              incomingScreen,
            ],
          );
        }

        return incomingScreen;
      },
    );
  }
}