import 'package:flutter/material.dart';

/// A collection of custom page transitions for smoother navigation experiences
class CustomPageTransitions {
  /// Creates a right-to-left slide transition with FAB animating to bottom left
  /// Great for floating action button to detail screen transitions
  static Route createFabToScreenTransition({
    required BuildContext context, 
    required Widget screen,
    required Offset startOffset,
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => screen,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // The main page slides in from right to left
        final slideAnimation = Tween<Offset>(
          begin: const Offset(1.0, 0.0),  // Start offscreen to the right
          end: Offset.zero                // End at normal position
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
          ),
        );
        
        // Fade in the new page
        final fadeAnimation = Tween<double>(begin: 0.2, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.8, curve: Curves.easeInOut),
          ),
        );
        
        // Add a subtle scale effect for depth
        final scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
          ),
        );
        
        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: ScaleTransition(
              scale: scaleAnimation, 
              child: child,
            ),
          ),
        );
      },
    );
  }
  
  /// Creates a material-style transition with a subtle scale and elevation effect
  static Route createElevatedTransition({
    required BuildContext context, 
    required Widget screen,
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => screen,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
          ),
        );
        
        final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          ),
        );
        
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            return Material(
              color: Colors.transparent,
              elevation: animation.value * 8,
              child: Transform.scale(
                scale: scaleAnimation.value,
                child: FadeTransition(
                  opacity: fadeAnimation,
                  child: child,
                ),
              ),
            );
          },
          child: child,
        );
      },
    );
  }
}
